name: openfit
description: OpenFit - Your AI-powered fitness companion with voice coaching and real-time workout tracking.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2
  supabase_flutter: ^2.0.0
  flutter_riverpod: ^2.4.0
  go_router: ^13.0.0
  shared_preferences: ^2.2.0
  freezed_annotation: ^2.4.1
  json_annotation: ^4.8.1
  # Animation and UI packages
  animations: ^2.0.8
  # Performance and utilities
  collection: ^1.17.2
  http: ^1.1.0
  shimmer: ^3.0.0
  
  # Audio recording and playback
  record: ^5.1.2
  audioplayers: ^6.1.0
  permission_handler: ^11.3.1
  path_provider: ^2.1.4

  # WebSocket and ElevenLabs integration
  web_socket_channel: ^3.0.3
  crypto: ^3.0.3

  # Environment configuration
  flutter_dotenv: ^5.1.0
  
  # WebRTC for OpenAI Realtime API
  flutter_webrtc: ^0.12.6
  glossy: ^0.0.5

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  build_runner: ^2.4.6
  freezed: ^2.4.1
  json_serializable: ^6.7.1
  flutter_launcher_icons: ^0.14.3

flutter:
  uses-material-design: true

  # Include environment configuration file
  assets:
    - .env

  # Liberator font family for military-inspired typography
  fonts:
    - family: Liberator
      fonts:
        - asset: assets/fonts/Liberator-Light.otf
          weight: 300
        - asset: assets/fonts/Liberator-Medium.otf
          weight: 500
        - asset: assets/fonts/Liberator-Heavy.otf
          weight: 700

# Flutter Launcher Icons configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "icon.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "icon.png"
    background_color: "#0A0A0B"
    theme_color: "#FF6B35"
  windows:
    generate: true
    image_path: "icon.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "icon.png"
