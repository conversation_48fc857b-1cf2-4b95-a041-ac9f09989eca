import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/audio_quality_metrics.dart';

class OpenAIRealtimeWebRTCService {
  // WebRTC components
  RTCPeerConnection? _peerConnection;
  RTCDataChannel? _dataChannel;
  MediaStream? _localStream;
  
  // Supabase client
  final _supabase = Supabase.instance.client;
  
  // State management
  final _connectionStateController = StreamController<RTCPeerConnectionState>.broadcast();
  final _transcriptController = StreamController<TranscriptEvent>.broadcast();
  final _errorController = StreamController<String>.broadcast();
  
  // Configuration
  static const String _model = 'gpt-4o-realtime-preview-2024-12-17';
  static const String _voice = 'alloy';

  // Audio quality monitoring
  final _audioQualityController = StreamController<AudioQualityMetrics>.broadcast();
  Stream<AudioQualityMetrics> get audioQuality => _audioQualityController.stream;

  // Connection quality monitoring
  final _connectionQualityController = StreamController<ConnectionQualityMetrics>.broadcast();
  Stream<ConnectionQualityMetrics> get connectionQuality => _connectionQualityController.stream;

  // Audio level monitoring
  Timer? _audioLevelTimer;
  Timer? _connectionQualityTimer;
  double _currentAudioLevel = 0.0;
  double _backgroundNoiseLevel = 0.0;
  bool _isAudioQualityGood = true;

  // Quality monitoring debounce
  DateTime? _lastQualityWarning;
  int _poorQualityCount = 0;
  static const int _qualityWarningCooldown = 30; // seconds
  static const int _poorQualityThreshold = 3; // consecutive poor quality detections

  // Workout state
  Map<String, dynamic>? _currentWorkout;
  int _currentExerciseIndex = 0;
  int _currentSetIndex = 0;
  bool _isResting = false;
  int _restTimeRemaining = 0;
  Timer? _restTimer;
  bool _isPaused = false;
  DateTime? _pausedAt;
  Duration _totalPausedDuration = Duration.zero;
  Map<String, dynamic>? _pendingFunctionCall;
  bool _isProcessingResponse = false;
  
  final _workoutStateController = StreamController<Map<String, dynamic>>.broadcast();
  Stream<Map<String, dynamic>> get workoutState => _workoutStateController.stream;
  
  // Streams
  Stream<RTCPeerConnectionState> get connectionState => _connectionStateController.stream;
  Stream<TranscriptEvent> get transcripts => _transcriptController.stream;
  Stream<String> get errors => _errorController.stream;
  
  // Connection status
  bool get isConnected => _peerConnection?.connectionState == RTCPeerConnectionState.RTCPeerConnectionStateConnected;
  
  /// Initialize and connect to OpenAI Realtime API via WebRTC
  Future<void> connect() async {
    try {
      // Get ephemeral token (for now using direct API key for testing)
      final apiKey = dotenv.env['OPENAI_API_KEY'];
      if (apiKey == null || apiKey.isEmpty) {
        throw Exception('OpenAI API key not found. Please add OPENAI_API_KEY to your .env file');
      }
      
      debugPrint('🔌 Connecting to OpenAI Realtime API...');
      
      // Create peer connection
      final configuration = {
        'iceServers': [
          {'urls': 'stun:stun.l.google.com:19302'},
        ]
      };
      
      _peerConnection = await createPeerConnection(configuration);
      
      // Set up connection state monitoring
      _peerConnection!.onConnectionState = (RTCPeerConnectionState state) {
        debugPrint('🔌 Connection state: $state');
        _connectionStateController.add(state);
        
        if (state == RTCPeerConnectionState.RTCPeerConnectionStateFailed ||
            state == RTCPeerConnectionState.RTCPeerConnectionStateClosed) {
          _errorController.add('Connection lost: $state');
        }
      };
      
      // Set up ICE connection state monitoring
      _peerConnection!.onIceConnectionState = (RTCIceConnectionState state) {
        debugPrint('🧊 ICE connection state: $state');
      };
      
      // Set up to receive remote audio
      _peerConnection!.onAddStream = (MediaStream stream) {
        debugPrint('🎵 Received remote stream with ${stream.getAudioTracks().length} audio tracks');
        stream.getAudioTracks().forEach((track) {
          track.enabled = true;
          debugPrint('🔊 Audio track enabled: ${track.id}');
        });
      };
      
      _peerConnection!.onTrack = (RTCTrackEvent event) {
        debugPrint('🎵 Received remote track: ${event.track.kind}');
        if (event.track.kind == 'audio') {
          // Ensure audio track is enabled
          event.track.enabled = true;
          debugPrint('🔊 Audio track enabled and ready for playback');
          
          if (event.streams.isNotEmpty) {
            final stream = event.streams.first;
            debugPrint('📻 Audio stream ID: ${stream.id}');
          }
        }
      };
      
      // Get user media (microphone) with optimized settings for gym environments
      final mediaConstraints = {
        'audio': {
          'echoCancellation': true,
          'noiseSuppression': true,
          'autoGainControl': true,
          'sampleRate': 16000, // Optimized for speech recognition
          'channelCount': 1, // Mono audio for better processing
          'latency': 0.02, // Low latency for real-time interaction
          'volume': 1.0,
        },
        'video': false
      };
      
      _localStream = await navigator.mediaDevices.getUserMedia(mediaConstraints);
      
      // Add local audio track to peer connection
      final audioTrack = _localStream!.getAudioTracks().first;
      await _peerConnection!.addTrack(audioTrack, _localStream!);
      
      // Create data channel for events
      final dataChannelInit = RTCDataChannelInit()
        ..ordered = true;
      
      _dataChannel = await _peerConnection!.createDataChannel('oai-events', dataChannelInit);
      
      // Set up data channel event handlers
      _dataChannel!.onMessage = (RTCDataChannelMessage message) {
        _handleDataChannelMessage(message.text);
      };
      
      _dataChannel!.onDataChannelState = (RTCDataChannelState state) {
        debugPrint('📊 Data channel state: $state');
        if (state == RTCDataChannelState.RTCDataChannelOpen) {
          debugPrint('✅ Data channel is open and ready');
        }
      };
      
      // Create and set local description (offer)
      debugPrint('📤 Creating SDP offer...');
      final offer = await _peerConnection!.createOffer();
      await _peerConnection!.setLocalDescription(offer);
      
      debugPrint('📡 Sending offer to OpenAI...');
      // Send offer to OpenAI and get answer
      final response = await _sendOfferToOpenAI(offer.sdp!, apiKey);
      
      // Set remote description (answer)
      debugPrint('📥 Setting remote description...');
      final answer = RTCSessionDescription(response, 'answer');
      await _peerConnection!.setRemoteDescription(answer);
      
      debugPrint('✅ WebRTC connection established');

      // Start monitoring audio and connection quality
      _startQualityMonitoring();

      // Restore workout state if we have one
      await _restoreWorkoutStateOnReconnect();

    } catch (e) {
      debugPrint('Connection error: $e');
      final enhancedError = _createEnhancedError(e);
      _errorController.add(enhancedError.userFriendlyMessage);
      await disconnect();
      rethrow;
    }
  }
  
  /// Send SDP offer to OpenAI and get answer
  Future<String> _sendOfferToOpenAI(String sdp, String apiKey) async {
    final url = Uri.parse('https://api.openai.com/v1/realtime?model=$_model');
    
    // Use HttpClient for precise header control
    final httpClient = HttpClient();
    try {
      final request = await httpClient.postUrl(url);
      
      // Set headers exactly as needed
      request.headers.set('Authorization', 'Bearer $apiKey');
      request.headers.set('Content-Type', 'application/sdp');
      
      // Write the body
      request.write(sdp);
      
      // Send the request
      final response = await request.close();
      final responseBody = await response.transform(utf8.decoder).join();
      
      if (response.statusCode == 200 || response.statusCode == 201) {
        debugPrint('✅ Received SDP answer from OpenAI');
        return responseBody;
      } else {
        debugPrint('❌ Failed to get SDP answer: ${response.statusCode}');
        debugPrint('Response body: $responseBody');
        throw Exception('Failed to get SDP answer: ${response.statusCode} $responseBody');
      }
    } finally {
      httpClient.close();
    }
  }
  
  /// Handle incoming messages from data channel
  void _handleDataChannelMessage(String message) {
    try {
      final event = jsonDecode(message);
      final type = event['type'] as String;
      
      // Only log crucial conversation events to reduce noise
      const silentEvents = [
        'response.audio_transcript.delta',
        'response.audio.delta',
        'response.function_call_arguments.delta',
        'response.output_item.added',
        'response.content_part.added',
        'rate_limits.updated',
        'input_audio_buffer.speech_started',
        'input_audio_buffer.speech_stopped',
        'input_audio_buffer.committed',
        'conversation.item.input_audio_transcription.started',
        'conversation.item.input_audio_transcription.delta',
        'output_audio_buffer.started',
        'output_audio_buffer.stopped',
        'response.created',
        'response.audio_transcript.done',
        'response.content_part.done',
        'response.output_item.done',
        'session.updated',
      ];

      // Log crucial conversation events with clear markers
      if (!silentEvents.contains(type)) {
        if (type == 'conversation.item.created') {
          debugPrint('💬 New conversation item');
        } else if (type == 'response.done') {
          debugPrint('✅ AI response completed');
        } else if (type == 'response.function_call_arguments.done') {
          debugPrint('🔧 Function call completed');
        } else if (type == 'conversation.item.input_audio_transcription.completed') {
          debugPrint('🎤 User speech transcribed');
        } else {
          debugPrint('📡 Event: $type');
        }
      }
      
      switch (type) {
        case 'session.created':
          _handleSessionCreated(event);
          break;
          
        case 'conversation.item.created':
          _handleConversationItemCreated(event);
          break;
          
        case 'response.audio_transcript.delta':
          _handleTranscriptDelta(event, isUser: false);
          break;
          
        case 'conversation.item.input_audio_transcription.completed':
          _handleInputTranscriptionCompleted(event);
          break;
          
        case 'response.done':
          _handleResponseDone(event);
          break;
          
        case 'response.function_call_arguments.done':
          _handleFunctionCallArgumentsDone(event);
          break;
          
        case 'response.audio.done':
          debugPrint('🔊 Audio response completed');
          break;
          
        case 'error':
          _handleError(event);
          break;
          
        default:
          // Silently ignore other events
          break;
      }
    } catch (e) {
      debugPrint('❌ Error handling message: $e');
      _errorController.add('Message handling error: $e');
    }
  }
  
  /// Handle session created event
  void _handleSessionCreated(Map<String, dynamic> event) {
    debugPrint('✅ Session created');
    
    // Build context with actual workout data if available
    String workoutContext = '';
    if (_currentWorkout != null) {
      final exercises = _currentWorkout!['exercises'] as List;
      workoutContext = '''
        
        CURRENT WORKOUT: ${_currentWorkout!['name']}
        Total exercises: ${exercises.length}
        
        Exercise list:
        ''';
      for (int i = 0; i < exercises.length; i++) {
        final ex = exercises[i];
        workoutContext += '''
        ${i + 1}. ${ex['name']} - ${ex['sets']} sets of ${ex['reps'][0]} reps at ${ex['weight'][0]} lbs
        ''';
      }
      
      if (_currentExerciseIndex < exercises.length) {
        workoutContext += '''
        
        Current position: Exercise ${_currentExerciseIndex + 1} (${exercises[_currentExerciseIndex]['name']}), Set ${_currentSetIndex + 1} of ${exercises[_currentExerciseIndex]['sets']}
        ''';
      } else {
        workoutContext += '''
        
        Workout completed! All ${exercises.length} exercises finished.
        ''';
      }
    }
    
    // Update session with optimized configuration for gym environments
    final updateEvent = {
      'type': 'session.update',
      'session': {
        'modalities': ['text', 'audio'],
        'voice': _voice,
        'instructions': '''You are an elite AI fitness coach with expertise in exercise science, sports psychology, and personalized training. You understand the gym environment and adapt to each user's unique needs and communication style.

        COMMUNICATION STYLE & PERSONALITY:
        - Be encouraging, knowledgeable, and authentically supportive like a trusted training partner
        - Keep responses concise but impactful (8-12 words during active sets, longer during rest)
        - Use natural, conversational language with appropriate gym terminology
        - Match the user's energy: high-energy for motivated users, calm support for struggling users
        - Provide specific, actionable form cues and technique tips when needed
        - Celebrate achievements with genuine enthusiasm that feels earned, not forced
        - Use progressive motivation that builds throughout the workout

        ENHANCED NATURAL LANGUAGE UNDERSTANDING:
        You excel at understanding varied expressions in noisy gym environments. Recognize these patterns:

        SET COMPLETION EXPRESSIONS:
        - "I did 12 reps" / "Just finished 12" / "12 done" / "That was 12 at 135" / "Got 12"
        - "Completed 10" / "Finished 10 reps" / "10 with 185" / "Done with 10"
        - "Set complete" / "Finished that set" / "All done" / "That's it"
        - "12 reps, 135 pounds" / "10 at bodyweight" / "8 heavy ones"
        - Breathing patterns: "Whew, 12" / "Ugh, got 10" / "Phew, done"

        REST & RECOVERY REQUESTS:
        - "Start my rest" / "I need a break" / "Rest time" / "Timer please" / "Start timer"
        - "Need to rest" / "Break time" / "Let me rest" / "Time for a break"
        - "Start the clock" / "Begin rest" / "Rest period" / "Countdown please"
        - Heavy breathing followed by: "Rest" / "Break" / "Timer"

        NAVIGATION & PROGRESSION:
        - "What's next?" / "Next exercise?" / "What am I doing next?" / "What now?"
        - "Move on" / "Next one" / "Ready for next" / "What's coming up?"
        - "Done with this exercise" / "Finished this one" / "All sets done"

        DIFFICULTY & FEEDBACK:
        - "That was hard" / "Tough set" / "Really challenging" / "Brutal" / "Heavy"
        - "Easy set" / "Light" / "Too easy" / "Could do more" / "Felt good"
        - "Perfect" / "Good form" / "Felt strong" / "Nailed it" / "Smooth"
        - "Struggled" / "Hard to finish" / "Almost didn't make it" / "Barely got it"

        EXERCISE MODIFICATIONS:
        - "Skip this" / "Can't do this" / "Different exercise" / "Something else"
        - "Too heavy" / "Too light" / "Wrong weight" / "Adjust weight"
        - "Hurts" / "Pain" / "Uncomfortable" / "Doesn't feel right"

        ADVANCED CONTEXTUAL COACHING:
        Analyze performance patterns and provide intelligent, personalized coaching:

        PERFORMANCE-BASED RESPONSES:
        - Struggling (low reps, long pauses, heavy breathing): "Focus on form. You've got this!" + specific technique cues
        - Crushing it (high reps, fast completion): "Strong work! Consider adding weight next time."
        - Fatigue indicators (declining reps, longer pauses): Suggest extended rest, breathing techniques, or modifications
        - Consistent performance: Acknowledge reliability and suggest progressive challenges
        - Personal records: "New PR! That's what training is about!" with specific achievement recognition
        - Form concerns: Provide immediate, specific cues: "Slow the negative" / "Full range of motion"

        FATIGUE & ENERGY MANAGEMENT:
        - Early workout (sets 1-2): Focus on form, establish rhythm, build confidence
        - Mid-workout (sets 3-4): Maintain intensity, provide technique reminders, motivate through challenges
        - Late workout (final sets): Emphasize mental toughness, celebrate perseverance, focus on finishing strong
        - Between exercises: Assess energy, adjust rest times, prepare mentally for next movement

        PROGRESSIVE MOTIVATION SYSTEM:
        - Workout start: "Let's build something great today"
        - First successful set: "Perfect start, keep that momentum"
        - Midpoint struggles: "This is where champions are made"
        - Near completion: "You're stronger than you think, finish strong"
        - Workout complete: "Outstanding work! You earned every rep"

        INTELLIGENT WEIGHT & INTENSITY ADJUSTMENTS:
        - If user consistently exceeds target reps by 3+: Suggest weight increase
        - If user consistently falls short by 2+ reps: Suggest weight decrease or form check
        - If user reports "too easy": Challenge with increased weight or tempo changes
        - If user reports "too hard": Offer modifications, rest extensions, or weight reductions

        GYM ENVIRONMENT OPTIMIZATION:
        You excel in noisy gym environments with background music, equipment sounds, and conversations:

        AUDIO PROCESSING STRATEGIES:
        - Expect incomplete phrases due to background noise: "Got... 12 reps" = "Got 12 reps"
        - Handle breathing interruptions: "I did... *heavy breathing* ...10 reps"
        - Recognize gym terminology and slang: "Smashed 12" = "Completed 12 reps"
        - Account for equipment noise masking parts of speech
        - Be patient with repeated requests if audio is unclear
        - Confirm understanding when audio quality is poor: "Did you say 12 reps?"

        FUNCTION CALLING PRECISION:
        Always prioritize accuracy in function calls. When in doubt, ask for clarification:

        SET LOGGING ACCURACY:
        - ALWAYS extract both reps AND weight when possible
        - If weight is unclear, ask: "How much weight for those 12 reps?"
        - If reps are unclear, confirm: "How many reps did you complete?"
        - Default to 0 weight only for bodyweight exercises
        - Listen for difficulty cues to set appropriate difficulty parameter

        REST TIMER PRECISION:
        - Default to smart rest calculation unless user specifies duration
        - Listen for fatigue cues to adjust rest time appropriately
        - Confirm unusual rest requests: "Did you want 3 minutes rest?"

        WORKOUT CONTEXT:
        $workoutContext

        CRITICAL WORKOUT PROGRESSION RULES:
        - Each exercise has multiple sets (typically 3-4)
        - Users must complete ALL sets of an exercise before moving to the next exercise
        - After logging a set, if more sets remain for current exercise, prepare for next set of SAME exercise
        - Only advance to next exercise when ALL sets of current exercise are complete
        - Always use actual exercise names and data from the loaded workout
        - NEVER make up exercise names or data - use only what's in the current workout
        - NEVER add exercises beyond the original workout plan
        - When workout is complete (all exercises done), do NOT add more exercises
        - Maintain strict adherence to the planned workout structure
        
        ENHANCED VOICE COMMAND PATTERNS:
        Master these natural workout communication patterns:

        SET LOGGING VARIATIONS (use log_set function):
        - Direct: "I did 12 reps" / "12 reps" / "Got 12" / "Finished 12"
        - With weight: "12 at 135" / "10 with 185" / "8 reps, 225 pounds" / "15 bodyweight"
        - Casual: "Just crushed 10" / "Smashed 12" / "Nailed 8 heavy ones"
        - Breathing patterns: "*exhale* 12 reps" / "Whew... got 10" / "*panting* done, 8 reps"
        - Difficulty indicators: "Barely got 8" / "Easy 12" / "Struggled through 6"

        REST TIMER REQUESTS (use start_rest function):
        - Standard: "Start rest" / "Rest time" / "Timer" / "Break time"
        - Specific: "2 minute rest" / "Need 90 seconds" / "Long break please"
        - Fatigue-based: "Need extra rest" / "Feeling tired" / "Longer break"
        - Quick: "Quick rest" / "Short break" / "30 seconds"

        EXERCISE NAVIGATION (use next_exercise function):
        - Completion: "Done with this" / "All sets finished" / "Ready for next"
        - Inquiry: "What's next?" / "Next exercise?" / "What now?"
        - Progression: "Move on" / "Next one" / "Continue workout"

        WEIGHT ADJUSTMENTS (use modify_weight function):
        - Increases: "Too light" / "Add weight" / "Increase to 155" / "Heavier"
        - Decreases: "Too heavy" / "Drop weight" / "Reduce to 135" / "Lighter"
        - Specific: "Change to 185" / "Use 225" / "Make it 155 pounds"

        WORKOUT CONTROL & FEEDBACK:
        - Pause: "Hold on" / "Wait" / "Pause" / "Stop for a sec"
        - Resume: "Continue" / "Let's go" / "Resume" / "Keep going"
        - Complete: "I'm done" / "Finished" / "End workout" / "All done"
        - Help: "How do I do this?" / "Form tips?" / "Technique help?"
        - Status: "How am I doing?" / "Progress check" / "Sets left?"
        
        INTELLIGENT RESPONSE PATTERNS:

        SET CONFIRMATION RESPONSES (vary these for engagement):
        - First set: "Solid start!" / "Perfect form!" / "Great beginning!"
        - Middle sets: "Keep it up!" / "Strong work!" / "Maintaining pace!"
        - Final set: "Last one, make it count!" / "Finish strong!" / "You've got this!"
        - Personal records: "New PR! Outstanding!" / "That's a record!" / "Incredible!"
        - Struggling: "Every rep counts!" / "Push through!" / "You're stronger than you think!"

        PROGRESSION COMMUNICATION:
        After each logged set, provide:
        1. Performance acknowledgment: "Strong set 2!" / "Excellent 12 reps!"
        2. Progress update: "1 more set" / "2 sets remaining" / "Final set coming up"
        3. Next action: "Rest up" / "Ready for set 3?" / "Moving to [next exercise]"

        REST PERIOD COACHING:
        - Start: "Rest timer started. Breathe deep and recover."
        - Mid-rest: "Halfway through your rest. Stay focused."
        - Final 10 seconds: Count down "10, 9, 8... Let's go!"
        - Extended rest: "Take the time you need. Quality over speed."

        MOTIVATIONAL LANGUAGE SYSTEM:
        Use progressive motivation that builds throughout the workout:

        EARLY WORKOUT (Sets 1-3):
        - Focus on form and rhythm: "Perfect technique!" / "Find your groove!"
        - Build confidence: "You're dialed in!" / "Smooth execution!"

        MID WORKOUT (Sets 4-6):
        - Maintain intensity: "Keep that energy!" / "You're in the zone!"
        - Acknowledge effort: "This is where it counts!" / "Pushing through!"

        LATE WORKOUT (Final sets):
        - Mental toughness: "Champions finish strong!" / "This is your moment!"
        - Perseverance: "You've earned every rep!" / "Dig deep!"

        ADAPTIVE LANGUAGE BASED ON PERFORMANCE:
        - Exceeding targets: "You're on fire!" / "Crushing it!" / "Next level!"
        - Meeting targets: "Right on track!" / "Consistent!" / "Solid work!"
        - Below targets: "Every rep matters!" / "Building strength!" / "Progress over perfection!"

        CRITICAL OPERATIONAL RULES:
        - This workout has a fixed structure. NEVER suggest or add exercises beyond the original plan.
        - When all exercises are complete, congratulate and mark workout as finished.
        - For exercise replacements, only use exercises from the database, never invent new ones.
        - Always stay within the bounds of the current workout plan.
        - Prioritize user safety and proper form over performance metrics.''',
        'input_audio_transcription': {
          'model': 'whisper-1',
          'language': 'en', // Specify language for better accuracy in gym environments
        },
        'turn_detection': {
          'type': 'server_vad',
          'threshold': 0.75, // Optimized threshold for gym noise and heavy breathing (default: 0.5)
          'prefix_padding_ms': 600, // Extended padding for post-exercise breathing patterns (default: 300)
          'silence_duration_ms': 500, // Longer silence detection for gym environment (default: 200)
          'create_response': true,
          'interrupt_response': false, // Prevent accidental interruptions during exercises
        },
        'output_audio_format': 'pcm16',
        'tools': [
          {
            'type': 'function',
            'name': 'load_workout',
            'description': 'Load today\'s workout or a specific workout',
            'parameters': {
              'type': 'object',
              'properties': {
                'workout_id': {
                  'type': 'string',
                  'description': 'Optional workout ID. If not provided, loads today\'s workout',
                },
              },
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'log_set',
            'description': 'Log a completed set when user reports reps and weight. Use when user says things like "I did 12 reps", "Got 10 at 185", "Finished 8 with 225 pounds", etc.',
            'parameters': {
              'type': 'object',
              'properties': {
                'reps': {
                  'type': 'integer',
                  'description': 'Number of repetitions completed (extract from phrases like "12 reps", "got 10", "finished 8")',
                },
                'weight': {
                  'type': 'number',
                  'description': 'Weight used in pounds (extract from phrases like "at 185", "with 225 pounds", use 0 for bodyweight exercises)',
                },
                'difficulty': {
                  'type': 'string',
                  'enum': ['easy', 'moderate', 'hard', 'very_hard'],
                  'description': 'Infer from user language: "easy/light" = easy, "tough/hard/heavy" = hard, "barely made it/struggled" = very_hard'
                },
                'form_quality': {
                  'type': 'string',
                  'enum': ['excellent', 'good', 'needs_work'],
                  'description': 'Assess from user feedback: "perfect/smooth/nailed it" = excellent, "felt good" = good, "sloppy/rushed" = needs_work'
                },
                'user_feedback': {
                  'type': 'string',
                  'description': 'Capture any additional comments about the set performance, form, or feeling'
                }
              },
              'required': ['reps', 'weight'],
            },
          },
          {
            'type': 'function',
            'name': 'next_exercise',
            'description': 'Move to the next exercise in the workout',
            'parameters': {
              'type': 'object',
              'properties': {},
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'start_rest',
            'description': 'Start rest timer when user requests a break. Use for phrases like "start rest", "I need a break", "rest time", "timer please", "2 minute rest", etc.',
            'parameters': {
              'type': 'object',
              'properties': {
                'duration': {
                  'type': 'integer',
                  'description': 'Rest duration in seconds. Extract from specific requests like "2 minute rest" (120), "90 seconds" (90), "quick break" (60). Leave empty for smart calculation.',
                },
                'user_fatigue_level': {
                  'type': 'string',
                  'enum': ['low', 'moderate', 'high'],
                  'description': 'Infer fatigue from user language: "feeling good/strong" = low, "getting tired" = moderate, "exhausted/need longer rest" = high'
                },
              },
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'skip_rest',
            'description': 'Skip the current rest period',
            'parameters': {
              'type': 'object',
              'properties': {},
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'get_current_exercise',
            'description': 'Get information about the current exercise and set',
            'parameters': {
              'type': 'object',
              'properties': {},
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'complete_workout',
            'description': 'Mark the workout as completed',
            'parameters': {
              'type': 'object',
              'properties': {},
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'complete_workout_with_feedback',
            'description': 'Complete workout and collect user feedback. Use when user indicates they want to finish or when all exercises are done.',
            'parameters': {
              'type': 'object',
              'properties': {
                'feedback': {
                  'type': 'string',
                  'description': 'Optional user feedback about the workout',
                },
                'rating': {
                  'type': 'integer',
                  'description': 'Optional workout rating from 1-5',
                  'minimum': 1,
                  'maximum': 5,
                },
              },
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'previous_exercise',
            'description': 'Go back to the previous exercise',
            'parameters': {
              'type': 'object',
              'properties': {},
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'get_exercise_instructions',
            'description': 'Get detailed instructions for the current exercise',
            'parameters': {
              'type': 'object',
              'properties': {},
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'pause_workout',
            'description': 'Pause the current workout',
            'parameters': {
              'type': 'object',
              'properties': {},
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'resume_workout',
            'description': 'Resume a paused workout',
            'parameters': {
              'type': 'object',
              'properties': {},
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'modify_weight',
            'description': 'Modify weight for remaining sets when user requests changes. Use for phrases like "too heavy", "add weight", "change to 185", "make it lighter", etc.',
            'parameters': {
              'type': 'object',
              'properties': {
                'weight': {
                  'type': 'number',
                  'description': 'New weight in pounds. Extract from specific requests like "change to 185", "use 225", or calculate from relative requests like "add 10 pounds", "drop 20"',
                },
              },
              'required': ['weight'],
            },
          },
          {
            'type': 'function',
            'name': 'skip_exercise',
            'description': 'Skip the current exercise and move to the next one',
            'parameters': {
              'type': 'object',
              'properties': {},
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'add_rest_time',
            'description': 'Add more time to the current rest period',
            'parameters': {
              'type': 'object',
              'properties': {
                'seconds': {
                  'type': 'integer',
                  'description': 'Additional seconds to add to rest (default 30)',
                },
              },
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'replace_exercise',
            'description': 'Replace the current exercise with a different one',
            'parameters': {
              'type': 'object',
              'properties': {
                'new_exercise_name': {
                  'type': 'string',
                  'description': 'Name of the exercise to replace with',
                },
                'muscle_group': {
                  'type': 'string',
                  'description': 'Optional muscle group to filter exercises (e.g., "back", "chest", "legs")',
                },
              },
              'required': [],
            },
          },
          {
            'type': 'function',
            'name': 'search_exercises',
            'description': 'Search for available exercises in the database',
            'parameters': {
              'type': 'object',
              'properties': {
                'muscle_group': {
                  'type': 'string',
                  'description': 'Filter by muscle group (e.g., "back", "chest", "legs")',
                },
                'equipment': {
                  'type': 'string',
                  'description': 'Filter by equipment (e.g., "dumbbell", "barbell", "bodyweight")',
                },
              },
              'required': [],
            },
          },
        ],
        'tool_choice': 'auto',
      },
    };
    
    _sendEvent(updateEvent);
  }
  
  /// Handle conversation item created
  void _handleConversationItemCreated(Map<String, dynamic> event) {
    final item = event['item'];
    debugPrint('Conversation item created: ${item['type']}');
  }
  
  /// Handle transcript delta
  void _handleTranscriptDelta(Map<String, dynamic> event, {required bool isUser}) {
    final delta = event['delta'] as String;
    _transcriptController.add(TranscriptEvent(
      text: delta,
      isUser: isUser,
      isDelta: true,
    ));
  }
  
  /// Handle input transcription completed
  void _handleInputTranscriptionCompleted(Map<String, dynamic> event) {
    final transcript = event['transcript'] as String;
    _transcriptController.add(TranscriptEvent(
      text: transcript,
      isUser: true,
      isDelta: false,
      isComplete: true,
    ));
  }
  
  /// Handle response done
  void _handleResponseDone(Map<String, dynamic> event) {
    debugPrint('Response completed');
    final response = event['response'];
    
    // Reset processing flag
    _isProcessingResponse = false;
    
    // Check if we have a pending function call from function_call_arguments.done
    if (_pendingFunctionCall != null) {
      final functionCall = _pendingFunctionCall!;
      _pendingFunctionCall = null; // Clear it
      _handleFunctionCall(functionCall);
      return; // Don't process further
    }
    
    // Check if this is a function call response
    if (response['output'] != null && response['output'].isNotEmpty) {
      final output = response['output'][0];
      
      if (output['type'] == 'function_call') {
        // Handle function call
        _handleFunctionCall(output);
      } else if (output['type'] == 'message' && output['content'] != null) {
        // Handle regular message
        for (final content in output['content']) {
          if (content['type'] == 'audio' && content['transcript'] != null) {
            _transcriptController.add(TranscriptEvent(
              text: content['transcript'],
              isUser: false,
              isDelta: false,
              isComplete: true,
            ));
          }
        }
      }
    }
  }
  
  /// Handle function call arguments done
  void _handleFunctionCallArgumentsDone(Map<String, dynamic> event) {
    final functionName = event['name'] as String?;
    final arguments = event['arguments'] as String?;

    debugPrint('🔧 Function Call: $functionName');
    if (arguments != null && arguments.isNotEmpty) {
      try {
        final parsedArgs = jsonDecode(arguments);
        debugPrint('   Args: $parsedArgs');
      } catch (e) {
        debugPrint('   Args: $arguments');
      }
    }

    // Store the function call data for when response.done is received
    // We'll handle it there to avoid duplicate responses
    _pendingFunctionCall = {
      'name': event['name'],
      'call_id': event['call_id'],
      'arguments': event['arguments'],
    };
  }
  
  /// Handle function call
  void _handleFunctionCall(Map<String, dynamic> functionCall) async {
    final name = functionCall['name'];
    final callId = functionCall['call_id'];
    final arguments = jsonDecode(functionCall['arguments'] ?? '{}');
    
    dynamic result;
    
    switch (name) {
      case 'load_workout':
        result = await _loadWorkout(arguments['workout_id']);
        break;
        
      case 'log_set':
        result = await _logSet(
          arguments['reps'] as int,
          arguments['weight'] as num,
          difficulty: arguments['difficulty'] as String?,
          formQuality: arguments['form_quality'] as String?,
          userFeedback: arguments['user_feedback'] as String?,
        );
        break;
        
      case 'next_exercise':
        result = _nextExercise();
        break;
        
      case 'start_rest':
        result = _startRest(
          arguments['duration'] as int?,
          fatigueLevel: arguments['user_fatigue_level'] as String?,
        );
        break;
        
      case 'skip_rest':
        result = _skipRest();
        break;
        
      case 'get_current_exercise':
        result = _getCurrentExercise();
        break;
        
      case 'complete_workout':
        result = await _completeWorkout();
        break;

      case 'complete_workout_with_feedback':
        final feedback = arguments['feedback'] as String?;
        final rating = arguments['rating'] as int?;
        result = await _completeWorkoutWithFeedback(feedback, rating);
        break;
        
      case 'previous_exercise':
        result = _previousExercise();
        break;
        
      case 'get_exercise_instructions':
        result = await _getExerciseInstructions();
        break;
        
      case 'pause_workout':
        result = _pauseWorkout();
        break;
        
      case 'resume_workout':
        result = _resumeWorkout();
        break;
        
      case 'modify_weight':
        result = _modifyWeight(arguments['weight'] as num);
        break;
        
      case 'skip_exercise':
        result = _skipExercise();
        break;
        
      case 'add_rest_time':
        result = _addRestTime(arguments['seconds'] as int?);
        break;
        
      case 'replace_exercise':
        result = await _replaceExercise(
          arguments['new_exercise_name'] as String?,
          arguments['muscle_group'] as String?,
        );
        break;
        
      case 'search_exercises':
        result = await _searchExercises(
          arguments['muscle_group'] as String?,
          arguments['equipment'] as String?,
        );
        break;
        
      default:
        result = {'error': 'Unknown function'};
    }
    
    // Send function result back
    final resultEvent = {
      'type': 'conversation.item.create',
      'item': {
        'type': 'function_call_output',
        'call_id': callId,
        'output': jsonEncode(result),
      },
    };
    
    _sendEvent(resultEvent);
    
    // Trigger a response to continue the conversation
    // Add a small delay to avoid "Conversation already has an active response" error
    Future.delayed(const Duration(milliseconds: 100), () {
      if (!_isProcessingResponse) {
        _isProcessingResponse = true;
        _sendEvent({
          'type': 'response.create',
          'response': {
            'modalities': ['text', 'audio'],
          }
        });
      }
    });
  }
  
  // Workout tracking functions
  Future<Map<String, dynamic>> _loadWorkout(String? workoutId) async {
    try {
      final userId = _supabase.auth.currentUser?.id;
      if (userId == null) {
        return {
          'error': 'User not authenticated',
          'message': 'Please sign in to track your workouts'
        };
      }
      
      // Load today's workout or specific workout
      Map<String, dynamic>? workout;
      
      if (workoutId != null) {
        // Load specific workout
        final response = await _supabase
            .from('workouts')
            .select('*, workout_exercises(*, exercises(*))')
            .eq('id', workoutId)
            .single();
        workout = response;
      } else {
        // Load today's incomplete workout
        final response = await _supabase
            .from('workouts')
            .select('*, workout_exercises(*, exercises(*))')
            .eq('user_id', userId)
            .or('is_completed.is.null,is_completed.eq.false')
            .order('created_at', ascending: false)
            .limit(1);
        
        if (response.isNotEmpty) {
          workout = response.first;
        }
      }
      
      if (workout == null) {
        return {
          'error': 'No workout found',
          'message': 'You don\'t have any active workouts. Please create a workout first.'
        };
      }
      
      // Transform workout data
      final exercises = (workout['workout_exercises'] as List).map((we) {
        final exercise = we['exercises'];
        return {
          'id': we['id'],
          'exercise_id': we['exercise_id'],
          'name': we['name'],
          'sets': we['sets'] ?? 3,
          'reps': we['reps'] ?? [10, 10, 10],
          'weight': we['weight'] ?? [0, 0, 0],
          'rest': we['rest_interval'] ?? 60,
          'primary_muscle': exercise['primary_muscle'] ?? '',
          'equipment': exercise['equipment'] ?? '',
          'order': we['order_index'] ?? 0,
        };
      }).toList();
      
      // Sort by order
      exercises.sort((a, b) => (a['order'] as int).compareTo(b['order'] as int));
      
      _currentWorkout = {
        'id': workout['id'],
        'name': workout['name'],
        'exercises': exercises,
        'started_at': DateTime.now().toIso8601String(),
      };
      
      // Always start fresh from the beginning when loading a workout
      _currentExerciseIndex = 0;
      _currentSetIndex = 0;
      _isPaused = false;
      _pausedAt = null;
      _totalPausedDuration = Duration.zero;
      
      debugPrint('📊 Loaded workout: ${workout['name']} with ${exercises.length} exercises');
      debugPrint('   Starting at: Exercise 1 (${exercises[0]['name']}), Set 1');
      
      // Don't load previously completed sets to avoid state corruption
      // await _loadCompletedSets();
      
      _updateWorkoutState();
      
      if (exercises.isEmpty) {
        return {
          'workout': workout['name'],
          'message': 'No exercises in this workout',
        };
      }
      
      final currentExercise = exercises[_currentExerciseIndex];
      // Update AI context with loaded workout
      _updateAIContext();
      
      return {
        'workout': workout['name'],
        'first_exercise': currentExercise['name'],
        'current_exercise_index': _currentExerciseIndex,
        'current_set': _currentSetIndex + 1,
        'sets': currentExercise['sets'],
        'target_reps': currentExercise['reps'][0],
        'target_weight': currentExercise['weight'][0],
        'total_exercises': exercises.length,
        'message': 'Loaded ${workout['name']}. Starting with ${currentExercise['name']}, set 1 of ${currentExercise['sets']}. Target: ${currentExercise['reps'][0]} reps at ${currentExercise['weight'][0]} pounds.',
      };
    } catch (e) {
      debugPrint('Error loading workout: $e');
      return {'error': 'Failed to load workout: ${e.toString()}'};
    }
  }
  
  Future<void> _loadCompletedSets() async {
    if (_currentWorkout == null) return;
    
    try {
      // Load any completed sets for this workout
      final response = await _supabase
          .from('completed_sets')
          .select('*')
          .eq('workout_id', _currentWorkout!['id'])
          .order('created_at', ascending: false);
      
      if (response.isNotEmpty) {
        // Find the latest exercise and set completed
        for (final set in response) {
          final exercises = _currentWorkout!['exercises'] as List;
          final exerciseIndex = exercises.indexWhere((e) => e['id'] == set['workout_exercise_id']);
          if (exerciseIndex != -1) {
            final setOrder = set['performed_set_order'] ?? 1;
            // Update current position if this is further along
            if (exerciseIndex > _currentExerciseIndex || 
                (exerciseIndex == _currentExerciseIndex && setOrder > _currentSetIndex)) {
              _currentExerciseIndex = exerciseIndex;
              _currentSetIndex = setOrder;
            }
          }
        }
      }
    } catch (e) {
      debugPrint('Error loading completed sets: $e');
    }
  }
  
  Future<Map<String, dynamic>> _logSet(int reps, num weight, {
    String? difficulty,
    String? formQuality,
    String? userFeedback,
  }) async {
    if (_currentWorkout == null) {
      return {'error': 'No workout loaded', 'message': 'Please start a workout first'};
    }
    
    final exercises = _currentWorkout!['exercises'] as List;
    if (_currentExerciseIndex >= exercises.length) {
      return {'error': 'Workout already completed', 'message': 'Great job finishing your workout!'};
    }
    
    // Validate indices to prevent corruption
    if (_currentExerciseIndex < 0 || _currentExerciseIndex >= exercises.length) {
      debugPrint('⚠️ Invalid exercise index: $_currentExerciseIndex, resetting to 0');
      _currentExerciseIndex = 0;
      _currentSetIndex = 0;
    }
    
    final currentExercise = exercises[_currentExerciseIndex];
    final totalSets = currentExercise['sets'] as int;
    final exerciseId = currentExercise['exercise_id'];
    
    // Validate set index
    if (_currentSetIndex >= totalSets) {
      debugPrint('⚠️ Invalid set index: $_currentSetIndex for exercise with $totalSets sets');
      return {'error': 'All sets completed for this exercise', 'message': 'Please move to the next exercise'};
    }
    
    try {
      // Check for personal record
      bool isPersonalRecord = false;
      if (weight > 0) {
        final prCheck = await _supabase
            .from('completed_sets')
            .select('performed_weight')
            .eq('workout_exercise_id', currentExercise['id'])
            .order('performed_weight', ascending: false)
            .limit(1);
        
        if (prCheck.isEmpty || (prCheck.first['performed_weight'] as num) < weight) {
          isPersonalRecord = true;
        }
      }
      
      // Map difficulty to database enum values
      String? difficultyFeedback;
      if (difficulty != null) {
        switch (difficulty.toLowerCase()) {
          case 'easy':
            difficultyFeedback = 'easy';
            break;
          case 'moderate':
            difficultyFeedback = 'medium';
            break;
          case 'hard':
          case 'very_hard':
            difficultyFeedback = 'hard';
            break;
        }
      }

      // Save to Supabase completed_sets table (matching exact schema structure)
      final setData = {
        'workout_id': _currentWorkout!['id'],
        'workout_exercise_id': currentExercise['id'],
        'performed_set_order': _currentSetIndex + 1,
        'performed_reps': reps,
        'performed_weight': weight.round(), // Round weight to match integer schema
        'set_feedback_difficulty': difficultyFeedback,
        'created_at': DateTime.now().toIso8601String(),
      };

      // Validate data before insertion
      if (!_validateSetData(setData)) {
        throw Exception('Invalid set data: failed validation');
      }

      await _supabase.from('completed_sets').insert(setData);

      debugPrint('✅ Successfully saved set to database: Exercise ${currentExercise['name']}, Set ${_currentSetIndex + 1}, $reps reps @ ${weight}lbs');
      
      // Track in local state for PR detection
      if (!_currentWorkout!.containsKey('completed_sets')) {
        _currentWorkout!['completed_sets'] = [];
      }
      final completedSetNumber = _currentSetIndex + 1; // Store before incrementing
      
      _currentWorkout!['completed_sets'].add({
        'exercise': currentExercise['name'],
        'exercise_index': _currentExerciseIndex,
        'set': completedSetNumber,
        'reps': reps,
        'weight': weight,
        'timestamp': DateTime.now().toIso8601String(),
        'is_pr': isPersonalRecord,
      });
      
      debugPrint('💾 Saved set to local state: ${currentExercise['name']} - Set $completedSetNumber');
      
      _currentSetIndex++;
      
      final setsRemaining = totalSets - _currentSetIndex;
      final isLastSet = setsRemaining == 0;
      
      debugPrint('📈 Set logged: Exercise ${_currentExerciseIndex + 1}/${exercises.length}, Set $completedSetNumber/$totalSets');
      debugPrint('   Sets remaining for ${currentExercise['name']}: $setsRemaining');
      
      if (_currentSetIndex >= totalSets) {
        // Completed all sets for this exercise
        debugPrint('✅ All sets completed for ${currentExercise['name']}');
        
        if (_currentExerciseIndex < exercises.length - 1) {
          // Move to next exercise only if not the last exercise
          _currentExerciseIndex++;
          _currentSetIndex = 0;
          final nextExercise = exercises[_currentExerciseIndex];
          debugPrint('➡️ Moving to next exercise: ${nextExercise['name']}');
        } else {
          debugPrint('🎉 All exercises completed!');
          // Don't add any new exercises - workout is complete
        }
      }
      
      _updateWorkoutState();
      
      // Build contextual coaching response based on performance feedback
      String message = _buildContextualCoachingMessage(
        isPersonalRecord: isPersonalRecord,
        difficulty: difficulty,
        formQuality: formQuality,
        userFeedback: userFeedback,
        reps: reps,
        targetReps: currentExercise['reps'][_currentSetIndex.clamp(0, (currentExercise['reps'] as List).length - 1)],
        exerciseName: currentExercise['name'],
        completedSetNumber: completedSetNumber,
        totalSets: totalSets,
        setsRemaining: setsRemaining,
      );

      // Add progression information
      if (setsRemaining > 0) {
        message += ' $setsRemaining ${setsRemaining == 1 ? "set" : "sets"} remaining for ${currentExercise['name']}.';

        // Add coaching based on difficulty
        if (difficulty == 'easy') {
          message += ' Consider increasing weight for the next set if you\'re feeling strong!';
        } else if (difficulty == 'very_hard') {
          message += ' Take a good rest - that was a tough one!';
        }
      } else if (_currentExerciseIndex < exercises.length - 1) {
        final nextExercise = exercises[_currentExerciseIndex];
        message += ' All sets completed! Next up: ${nextExercise['name']}.';
      } else {
        message += ' Congratulations! You\'ve completed the entire workout! All ${exercises.length} exercises done.';
      }
      
      return {
        'logged': true,
        'exercise': currentExercise['name'],
        'set': _currentSetIndex,
        'reps': reps,
        'weight': weight,
        'sets_remaining': setsRemaining,
        'is_last_set': isLastSet,
        'is_personal_record': isPersonalRecord,
        'message': message,
        'rest_duration': currentExercise['rest'] ?? 60,
      };
    } catch (e) {
      debugPrint('❌ Error logging set: $e');

      // Provide specific error messages based on error type
      String errorMessage;
      if (e.toString().contains('validation')) {
        errorMessage = 'Invalid workout data. Please check your input and try again.';
      } else if (e.toString().contains('network') || e.toString().contains('connection')) {
        errorMessage = 'Network error. Your set will be saved locally and synced when connection is restored.';
        // TODO: Implement local storage fallback
      } else if (e.toString().contains('permission') || e.toString().contains('auth')) {
        errorMessage = 'Authentication error. Please sign in again to continue tracking.';
      } else {
        errorMessage = 'Failed to save set. Please try logging it again.';
      }

      return {
        'error': true,
        'error_code': 'SET_LOG_FAILED',
        'message': errorMessage,
        'technical_error': e.toString(),
        'retry_suggested': true,
      };
    }
  }

  /// Build contextual coaching message based on performance feedback
  String _buildContextualCoachingMessage({
    required bool isPersonalRecord,
    String? difficulty,
    String? formQuality,
    String? userFeedback,
    required int reps,
    required int targetReps,
    required String exerciseName,
    required int completedSetNumber,
    required int totalSets,
    required int setsRemaining,
  }) {
    String message = '';

    // Start with achievement recognition
    if (isPersonalRecord) {
      message = '🏆 Personal record! That\'s incredible work!';
    } else if (reps > targetReps) {
      message = '💪 Excellent! You crushed the target reps!';
    } else if (reps == targetReps) {
      message = '✅ Perfect! Right on target!';
    } else {
      message = '👍 Good work!';
    }

    // Add difficulty-based coaching
    if (difficulty != null) {
      switch (difficulty.toLowerCase()) {
        case 'easy':
          message += ' That looked smooth - you might be ready for more weight!';
          break;
        case 'moderate':
          message += ' Nice controlled effort!';
          break;
        case 'hard':
          message += ' That was challenging but you powered through!';
          break;
        case 'very_hard':
          message += ' Wow, that was tough but you didn\'t give up! Impressive determination!';
          break;
      }
    }

    // Add form feedback
    if (formQuality != null) {
      switch (formQuality.toLowerCase()) {
        case 'excellent':
          message += ' Your form looked excellent!';
          break;
        case 'good':
          message += ' Good form throughout the set!';
          break;
        case 'needs_work':
          message += ' Focus on your form for the next set - quality over quantity!';
          break;
      }
    }

    // Add set progress
    message += ' That was set $completedSetNumber of $totalSets for $exerciseName.';

    return message;
  }

  /// Validate set data before database insertion
  bool _validateSetData(Map<String, dynamic> setData) {
    try {
      // Check required fields
      if (setData['workout_id'] == null || setData['workout_id'].toString().isEmpty) {
        debugPrint('❌ Validation failed: Missing workout_id');
        return false;
      }

      if (setData['workout_exercise_id'] == null || setData['workout_exercise_id'].toString().isEmpty) {
        debugPrint('❌ Validation failed: Missing workout_exercise_id');
        return false;
      }

      // Validate numeric fields
      final reps = setData['performed_reps'];
      if (reps == null || reps is! int || reps < 0 || reps > 1000) {
        debugPrint('❌ Validation failed: Invalid reps value: $reps');
        return false;
      }

      final weight = setData['performed_weight'];
      if (weight == null || weight is! int || weight < 0 || weight > 10000) {
        debugPrint('❌ Validation failed: Invalid weight value: $weight');
        return false;
      }

      final setOrder = setData['performed_set_order'];
      if (setOrder == null || setOrder is! int || setOrder < 1 || setOrder > 100) {
        debugPrint('❌ Validation failed: Invalid set order: $setOrder');
        return false;
      }

      // Validate difficulty enum if provided
      final difficulty = setData['set_feedback_difficulty'];
      if (difficulty != null && !['easy', 'medium', 'hard'].contains(difficulty)) {
        debugPrint('❌ Validation failed: Invalid difficulty: $difficulty');
        return false;
      }

      // Validate timestamp format
      final timestamp = setData['created_at'];
      if (timestamp != null) {
        try {
          DateTime.parse(timestamp);
        } catch (e) {
          debugPrint('❌ Validation failed: Invalid timestamp format: $timestamp');
          return false;
        }
      }

      debugPrint('✅ Set data validation passed');
      return true;
    } catch (e) {
      debugPrint('❌ Validation error: $e');
      return false;
    }
  }

  /// Validate workout completion data
  bool _validateWorkoutCompletion(String workoutId, String workoutName, List completedSets, List exercises) {
    try {
      // Check required fields
      if (workoutId.isEmpty || workoutName.isEmpty) {
        debugPrint('❌ Workout completion validation failed: Missing workout ID or name');
        return false;
      }

      // Validate exercises structure
      if (exercises.isEmpty) {
        debugPrint('❌ Workout completion validation failed: No exercises in workout');
        return false;
      }

      // Check if user has completed at least one set
      if (completedSets.isEmpty) {
        debugPrint('⚠️ Workout completion warning: No sets completed, but allowing completion');
        // Allow completion even with no sets (user might have skipped everything)
      }

      // Validate completed sets structure
      for (final set in completedSets) {
        if (set is! Map ||
            !set.containsKey('reps') ||
            !set.containsKey('weight') ||
            !set.containsKey('exercise')) {
          debugPrint('❌ Workout completion validation failed: Invalid completed set structure');
          return false;
        }
      }

      debugPrint('✅ Workout completion validation passed');
      return true;
    } catch (e) {
      debugPrint('❌ Workout completion validation error: $e');
      return false;
    }
  }

  /// Calculate smart rest duration based on exercise type and user state
  int _calculateSmartRestDuration({
    required int baseRest,
    required String exerciseName,
    required String primaryMuscle,
    String? fatigueLevel,
  }) {
    int adjustedRest = baseRest;

    // Adjust based on exercise type and muscle group
    final exerciseNameLower = exerciseName.toLowerCase();

    // Compound movements need more rest
    if (exerciseNameLower.contains('squat') ||
        exerciseNameLower.contains('deadlift') ||
        exerciseNameLower.contains('bench press') ||
        exerciseNameLower.contains('overhead press') ||
        exerciseNameLower.contains('row')) {
      adjustedRest = (baseRest * 1.2).round(); // 20% more rest
    }

    // Large muscle groups need more rest
    if (primaryMuscle.toLowerCase().contains('legs') ||
        primaryMuscle.toLowerCase().contains('back') ||
        primaryMuscle.toLowerCase().contains('chest')) {
      adjustedRest = (adjustedRest * 1.1).round(); // 10% more rest
    }

    // Adjust based on user fatigue level
    if (fatigueLevel != null) {
      switch (fatigueLevel.toLowerCase()) {
        case 'low':
          adjustedRest = (adjustedRest * 0.8).round(); // 20% less rest
          break;
        case 'moderate':
          // Keep as calculated
          break;
        case 'high':
          adjustedRest = (adjustedRest * 1.3).round(); // 30% more rest
          break;
      }
    }

    // Ensure minimum and maximum bounds
    return adjustedRest.clamp(30, 300); // 30 seconds to 5 minutes
  }

  Map<String, dynamic> _nextExercise() {
    if (_currentWorkout == null) {
      return {'error': 'No workout loaded'};
    }
    
    final exercises = _currentWorkout!['exercises'] as List;
    
    // Check if we're already at or beyond the last exercise
    if (_currentExerciseIndex >= exercises.length - 1) {
      return {
        'completed': true, 
        'message': 'You\'ve already completed all exercises! Great workout!',
        'total_exercises': exercises.length,
      };
    }
    
    _currentExerciseIndex++;
    _currentSetIndex = 0;
    
    final nextExercise = exercises[_currentExerciseIndex];
    _updateWorkoutState();
    
    return {
      'exercise': nextExercise['name'],
      'sets': nextExercise['sets'],
      'target_reps': nextExercise['reps'][0],
      'target_weight': nextExercise['weight'][0],
      'primary_muscle': nextExercise['primary_muscle'],
    };
  }
  
  Map<String, dynamic> _startRest(int? duration, {String? fatigueLevel}) {
    if (_currentWorkout == null) {
      return {'error': 'No workout loaded'};
    }

    final exercises = _currentWorkout!['exercises'] as List;
    final currentExercise = exercises[_currentExerciseIndex];

    // Smart rest duration calculation
    int restDuration;
    if (duration != null) {
      restDuration = duration;
    } else {
      restDuration = _calculateSmartRestDuration(
        baseRest: currentExercise['rest'] as int,
        exerciseName: currentExercise['name'],
        primaryMuscle: currentExercise['primary_muscle'],
        fatigueLevel: fatigueLevel,
      );
    }
    
    _isResting = true;
    _restTimeRemaining = restDuration;
    
    _restTimer?.cancel();
    _restTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      _restTimeRemaining--;
      _updateWorkoutState();
      
      if (_restTimeRemaining <= 0) {
        timer.cancel();
        _isResting = false;
        _updateWorkoutState();
      }
    });
    
    // Build smart rest message
    String restMessage = 'Rest timer started for $restDuration seconds.';

    if (duration == null) {
      // Explain the smart calculation
      final baseRest = currentExercise['rest'] as int;
      if (restDuration != baseRest) {
        if (restDuration > baseRest) {
          restMessage += ' Extended from $baseRest seconds for better recovery.';
        } else {
          restMessage += ' Reduced from $baseRest seconds - you\'re feeling strong!';
        }
      }
    }

    return {
      'resting': true,
      'duration': restDuration,
      'base_duration': currentExercise['rest'] as int,
      'is_smart_adjusted': duration == null && restDuration != (currentExercise['rest'] as int),
      'message': restMessage,
    };
  }
  
  Map<String, dynamic> _skipRest() {
    _restTimer?.cancel();
    _isResting = false;
    _restTimeRemaining = 0;
    _updateWorkoutState();
    
    return {'rest_skipped': true};
  }
  
  Map<String, dynamic> _getCurrentExercise() {
    if (_currentWorkout == null) {
      return {'error': 'No workout loaded'};
    }
    
    final exercises = _currentWorkout!['exercises'] as List;
    
    // Validate indices
    if (_currentExerciseIndex < 0 || _currentExerciseIndex >= exercises.length) {
      return {
        'completed': true, 
        'message': 'Great job! You\'ve completed all ${exercises.length} exercises in your workout!',
        'total_exercises_completed': exercises.length,
        'workout_name': _currentWorkout!['name'],
      };
    }
    
    final currentExercise = exercises[_currentExerciseIndex];
    final repsArray = currentExercise['reps'] as List;
    final weightArray = currentExercise['weight'] as List;
    
    // Get target reps/weight with bounds checking
    final targetReps = _currentSetIndex < repsArray.length ? repsArray[_currentSetIndex] : repsArray[0];
    final targetWeight = _currentSetIndex < weightArray.length ? weightArray[_currentSetIndex] : weightArray[0];
    
    return {
      'exercise': currentExercise['name'],
      'exercise_number': _currentExerciseIndex + 1,
      'total_exercises': exercises.length,
      'set': _currentSetIndex + 1,
      'total_sets': currentExercise['sets'],
      'target_reps': targetReps,
      'target_weight': targetWeight,
      'primary_muscle': currentExercise['primary_muscle'],
      'is_resting': _isResting,
      'rest_remaining': _restTimeRemaining,
      'message': 'Currently on ${currentExercise['name']} (exercise ${_currentExerciseIndex + 1} of ${exercises.length}), set ${_currentSetIndex + 1} of ${currentExercise['sets']}. Target: $targetReps reps at $targetWeight pounds.',
    };
  }
  
  Future<Map<String, dynamic>> _completeWorkout() async {
    if (_currentWorkout == null) {
      return {'error': 'No workout loaded'};
    }

    try {
      final workoutId = _currentWorkout!['id'];
      final workoutName = _currentWorkout!['name'];
      final completedSets = _currentWorkout!['completed_sets'] ?? [];
      final exercises = _currentWorkout!['exercises'] as List;

      // Validate workout data before completion
      if (!_validateWorkoutCompletion(workoutId, workoutName, completedSets, exercises)) {
        return {
          'error': 'Invalid workout data',
          'message': 'Cannot complete workout due to data validation errors',
        };
      }

      // Calculate workout summary with enhanced metrics
      final totalSetsCompleted = completedSets.length;
      final exercisesCompleted = _currentExerciseIndex;
      final totalSetsPlanned = exercises.fold<int>(0, (sum, ex) => sum + (ex['sets'] as int));
      final completionPercentage = totalSetsPlanned > 0 ? (totalSetsCompleted / totalSetsPlanned * 100).round() : 0;

      // Calculate duration accounting for paused time
      final startedAt = DateTime.parse(_currentWorkout!['started_at']);
      final totalDuration = DateTime.now().difference(startedAt);
      final activeDuration = totalDuration - _totalPausedDuration;
      final duration = activeDuration.inSeconds.clamp(0, 86400); // Max 24 hours
      
      // Create enhanced workout summary
      final workoutSummary = {
        'total_sets_completed': totalSetsCompleted,
        'total_sets_planned': totalSetsPlanned,
        'completion_percentage': completionPercentage,
        'exercises_completed': exercisesCompleted,
        'total_exercises': exercises.length,
        'workout_name': workoutName,
        'completed_at': DateTime.now().toIso8601String(),
        'duration_seconds': duration,
        'active_duration_seconds': duration,
        'paused_duration_seconds': _totalPausedDuration.inSeconds,
        'session_data': {
          'completed_sets': completedSets,
          'exercises': exercises.map((e) => e['name']).toList(),
          'tracking_method': 'hands_free_voice',
        },
        'performance_metrics': {
          'sets_per_minute': duration > 0 ? (totalSetsCompleted / (duration / 60)).toStringAsFixed(2) : '0',
          'average_rest_time': 'calculated_from_sets', // Could be enhanced
        },
      };
      
      // Save to Supabase completed_workouts table
      await _supabase.from('completed_workouts').insert({
        'workout_id': workoutId,
        'user_id': _supabase.auth.currentUser!.id,
        'date_completed': DateTime.now().toIso8601String(),
        'duration': duration,
        'user_feedback_completed_workout': 'Completed via hands-free voice tracking',
        'completed_workout_summary': workoutSummary,
        'calories_burned': 0, // Could calculate based on exercises
        'rating': null, // User can rate later
      });
      
      // Update workout as completed
      await _supabase.from('workouts').update({
        'is_completed': true,
        'end_time': DateTime.now().toIso8601String(),
      }).eq('id', workoutId);
      
      // Clear workout state
      _currentWorkout = null;
      _currentExerciseIndex = 0;
      _currentSetIndex = 0;
      _restTimer?.cancel();
      _isResting = false;
      _isPaused = false;
      _pausedAt = null;
      _totalPausedDuration = Duration.zero;
      _updateWorkoutState();
      
      debugPrint('✅ Workout completed successfully: $workoutName');
      debugPrint('   Sets completed: $totalSetsCompleted/$totalSetsPlanned ($completionPercentage%)');
      debugPrint('   Duration: ${(duration / 60).round()} minutes');

      return {
        'completed': true,
        'workout': workoutName,
        'total_sets': totalSetsCompleted,
        'total_sets_planned': totalSetsPlanned,
        'completion_percentage': completionPercentage,
        'exercises_completed': exercisesCompleted,
        'total_exercises': exercises.length,
        'duration_minutes': (duration / 60).round(),
        'message': _buildWorkoutCompletionMessage(completionPercentage, totalSetsCompleted, exercisesCompleted),
      };
    } catch (e) {
      debugPrint('❌ Error completing workout: $e');

      // Provide specific error handling
      String errorMessage;
      if (e.toString().contains('network') || e.toString().contains('connection')) {
        errorMessage = 'Network error. Your workout data has been saved locally and will sync when connection is restored.';
        // TODO: Implement local storage fallback
      } else if (e.toString().contains('permission') || e.toString().contains('auth')) {
        errorMessage = 'Authentication error. Please sign in again to save your workout.';
      } else if (e.toString().contains('validation')) {
        errorMessage = 'Data validation error. Some workout information may be incomplete.';
      } else {
        errorMessage = 'Failed to save workout. Please try again or contact support.';
      }

      return {
        'error': true,
        'error_code': 'WORKOUT_COMPLETION_FAILED',
        'message': errorMessage,
        'technical_error': e.toString(),
        'retry_suggested': true,
        'local_backup_available': false, // TODO: Implement local backup
      };
    }
  }

  /// Complete workout with user feedback and navigate to completion screen
  Future<Map<String, dynamic>> _completeWorkoutWithFeedback(String? feedback, int? rating) async {
    if (_currentWorkout == null) {
      return {'error': 'No workout loaded'};
    }

    try {
      // First complete the workout normally
      final completionResult = await _completeWorkout();

      if (completionResult['error'] != null) {
        return completionResult;
      }

      // Add feedback and rating to the result
      final enhancedResult = Map<String, dynamic>.from(completionResult);
      enhancedResult['user_feedback'] = feedback;
      enhancedResult['user_rating'] = rating;
      enhancedResult['navigate_to_completion'] = true;

      // Provide contextual completion message
      String completionMessage = 'Workout completed successfully! ';
      if (rating != null) {
        if (rating >= 4) {
          completionMessage += 'Glad you had a great workout! ';
        } else if (rating >= 3) {
          completionMessage += 'Thanks for the feedback! ';
        } else {
          completionMessage += 'Thanks for letting me know. We\'ll work on making it better! ';
        }
      }

      if (feedback != null && feedback.isNotEmpty) {
        completionMessage += 'Your feedback has been recorded.';
      }

      enhancedResult['message'] = completionMessage;

      debugPrint('✅ Workout completed with feedback - navigating to completion screen');

      return enhancedResult;
    } catch (e) {
      debugPrint('❌ Error completing workout with feedback: $e');
      return {
        'error': true,
        'message': 'Failed to complete workout with feedback: $e',
      };
    }
  }

  /// Build contextual workout completion message
  String _buildWorkoutCompletionMessage(int completionPercentage, int totalSetsCompleted, int exercisesCompleted) {
    if (completionPercentage >= 100) {
      return '🎉 Outstanding! You completed the entire workout! All $totalSetsCompleted sets done. You\'re crushing your fitness goals!';
    } else if (completionPercentage >= 80) {
      return '💪 Excellent work! You completed $completionPercentage% of your workout ($totalSetsCompleted sets). That\'s a solid training session!';
    } else if (completionPercentage >= 60) {
      return '👍 Good effort! You completed $completionPercentage% of your workout ($totalSetsCompleted sets). Every workout counts toward your progress!';
    } else if (completionPercentage >= 40) {
      return '✅ Nice start! You completed $completionPercentage% of your workout ($totalSetsCompleted sets). Building consistency is key!';
    } else if (totalSetsCompleted > 0) {
      return '🌟 Great job getting started! You completed $totalSetsCompleted sets. Every rep brings you closer to your goals!';
    } else {
      return '📝 Workout logged! Even though no sets were completed, tracking your sessions helps build the habit. Keep it up!';
    }
  }

  void _updateWorkoutState() {
    final state = {
      'has_workout': _currentWorkout != null,
      'workout_name': _currentWorkout?['name'],
      'workout_id': _currentWorkout?['id'],
      'current_exercise_index': _currentExerciseIndex,
      'current_set_index': _currentSetIndex,
      'is_resting': _isResting,
      'rest_time_remaining': _restTimeRemaining,
      'is_paused': _isPaused,
      'total_exercises': _currentWorkout != null ? (_currentWorkout!['exercises'] as List).length : 0,
      'current_exercise_name': _getCurrentExerciseName(),
    };

    // Add completed sets info if available
    if (_currentWorkout != null && _currentWorkout!.containsKey('completed_sets')) {
      state['completed_sets'] = _currentWorkout!['completed_sets'];
    }

    _workoutStateController.add(state);
  }

  String? _getCurrentExerciseName() {
    if (_currentWorkout == null) return null;
    final exercises = _currentWorkout!['exercises'] as List;
    if (_currentExerciseIndex >= 0 && _currentExerciseIndex < exercises.length) {
      return exercises[_currentExerciseIndex]['name'];
    }
    return null;
  }
  
  /// Update AI context with current workout information
  void _updateAIContext() {
    if (_currentWorkout == null || _dataChannel?.state != RTCDataChannelState.RTCDataChannelOpen) {
      return;
    }
    
    final exercises = _currentWorkout!['exercises'] as List;
    
    // Build updated context
    String workoutContext = '''
CURRENT WORKOUT: ${_currentWorkout!['name']}
Total exercises: ${exercises.length}

Exercise list:
''';
    
    for (int i = 0; i < exercises.length; i++) {
      final ex = exercises[i];
      workoutContext += '''
${i + 1}. ${ex['name']} - ${ex['sets']} sets of ${ex['reps'][0]} reps at ${ex['weight'][0]} lbs
''';
    }
    
    if (_currentExerciseIndex < exercises.length) {
      workoutContext += '''

Current position: Exercise ${_currentExerciseIndex + 1} (${exercises[_currentExerciseIndex]['name']}), Set ${_currentSetIndex + 1} of ${exercises[_currentExerciseIndex]['sets']}
''';
    } else {
      workoutContext += '''

Workout completed! All ${exercises.length} exercises finished.
''';
    }
    
    // Send context update
    final contextEvent = {
      'type': 'conversation.item.create',
      'item': {
        'type': 'message',
        'role': 'system',
        'content': [
          {
            'type': 'input_text',
            'text': 'Workout context update: $workoutContext',
          }
        ],
      },
    };
    
    _sendEvent(contextEvent);
    debugPrint('🔄 Updated AI context with workout information');
  }
  
  Map<String, dynamic> _previousExercise() {
    if (_currentWorkout == null) {
      return {'error': 'No workout loaded'};
    }
    
    if (_currentExerciseIndex > 0) {
      _currentExerciseIndex--;
      _currentSetIndex = 0;
      
      final exercises = _currentWorkout!['exercises'] as List;
      final exercise = exercises[_currentExerciseIndex];
      
      _updateWorkoutState();
      
      return {
        'exercise': exercise['name'],
        'sets': exercise['sets'],
        'target_reps': exercise['reps'][0],
        'target_weight': exercise['weight'][0],
        'message': 'Going back to ${exercise['name']}',
      };
    } else {
      return {'error': 'Already at the first exercise'};
    }
  }
  
  Future<Map<String, dynamic>> _getExerciseInstructions() async {
    if (_currentWorkout == null) {
      return {'error': 'No workout loaded'};
    }
    
    final exercises = _currentWorkout!['exercises'] as List;
    if (_currentExerciseIndex >= exercises.length) {
      return {'error': 'No current exercise'};
    }
    
    final currentExercise = exercises[_currentExerciseIndex];
    final exerciseId = currentExercise['exercise_id'];
    
    try {
      // Fetch detailed instructions from exercises table
      final response = await _supabase
          .from('exercises')
          .select('instructions, form_tips, common_mistakes')
          .eq('id', exerciseId)
          .single();
      
      return {
        'exercise': currentExercise['name'],
        'instructions': response['instructions'] ?? 'No instructions available',
        'form_tips': response['form_tips'],
        'common_mistakes': response['common_mistakes'],
        'primary_muscle': currentExercise['primary_muscle'],
        'equipment': currentExercise['equipment'],
      };
    } catch (e) {
      return {
        'exercise': currentExercise['name'],
        'instructions': 'Basic exercise instructions not available',
        'primary_muscle': currentExercise['primary_muscle'],
      };
    }
  }
  
  Map<String, dynamic> _pauseWorkout() {
    if (_currentWorkout == null) {
      return {'error': 'No workout active'};
    }
    
    if (_isPaused) {
      return {'error': 'Workout already paused'};
    }
    
    _isPaused = true;
    _pausedAt = DateTime.now();
    
    // Pause rest timer if active
    if (_isResting) {
      _restTimer?.cancel();
    }
    
    _updateWorkoutState();
    
    return {
      'paused': true,
      'message': 'Workout paused. Say "resume workout" when ready to continue.',
    };
  }
  
  Map<String, dynamic> _resumeWorkout() {
    if (!_isPaused) {
      return {'error': 'Workout is not paused'};
    }
    
    if (_pausedAt != null) {
      _totalPausedDuration += DateTime.now().difference(_pausedAt!);
    }
    
    _isPaused = false;
    _pausedAt = null;
    
    // Resume rest timer if was resting
    if (_isResting && _restTimeRemaining > 0) {
      _restTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        _restTimeRemaining--;
        _updateWorkoutState();
        
        if (_restTimeRemaining <= 0) {
          timer.cancel();
          _isResting = false;
          _updateWorkoutState();
        }
      });
    }
    
    _updateWorkoutState();
    
    return {
      'resumed': true,
      'message': _isResting ? 
        'Workout resumed. Rest timer continuing with $_restTimeRemaining seconds.' :
        'Workout resumed. Let\'s continue!',
    };
  }
  
  Map<String, dynamic> _modifyWeight(num newWeight) {
    if (_currentWorkout == null) {
      return {'error': 'No workout loaded'};
    }
    
    final exercises = _currentWorkout!['exercises'] as List;
    if (_currentExerciseIndex >= exercises.length) {
      return {'error': 'No current exercise'};
    }
    
    final currentExercise = exercises[_currentExerciseIndex];
    final weights = currentExercise['weight'] as List;
    
    // Update weight for remaining sets
    for (int i = _currentSetIndex; i < weights.length; i++) {
      weights[i] = newWeight;
    }
    
    _updateWorkoutState();
    
    return {
      'modified': true,
      'new_weight': newWeight,
      'exercise': currentExercise['name'],
      'message': 'Weight updated to $newWeight pounds for remaining sets',
    };
  }
  
  /// Skip current exercise and move to next
  Map<String, dynamic> _skipExercise() {
    if (_currentWorkout == null) {
      return {'error': 'No workout loaded'};
    }
    
    final exercises = _currentWorkout!['exercises'] as List;
    
    // Check if we're at or beyond the last exercise
    if (_currentExerciseIndex >= exercises.length - 1) {
      return {
        'error': 'Cannot skip - this is the last exercise!',
        'message': 'You\'re on the final exercise. Let\'s finish strong!',
        'current_exercise': exercises[_currentExerciseIndex]['name'],
        'exercise_number': _currentExerciseIndex + 1,
        'total_exercises': exercises.length,
      };
    }
    
    final skippedExercise = exercises[_currentExerciseIndex];
    
    // Move to next exercise
    _currentExerciseIndex++;
    _currentSetIndex = 0;
    
    final nextExercise = exercises[_currentExerciseIndex];
    _updateWorkoutState();
    
    return {
      'skipped': skippedExercise['name'],
      'next_exercise': nextExercise['name'],
      'sets': nextExercise['sets'],
      'target_reps': nextExercise['reps'][0],
      'target_weight': nextExercise['weight'][0],
      'message': 'Skipped ${skippedExercise['name']}. Moving to ${nextExercise['name']} (exercise ${_currentExerciseIndex + 1} of ${exercises.length}).',
    };
  }
  
  /// Add more time to current rest period
  Map<String, dynamic> _addRestTime(int? additionalSeconds) {
    if (!_isResting) {
      return {'error': 'Not currently resting'};
    }
    
    final secondsToAdd = additionalSeconds ?? 30;
    _restTimeRemaining += secondsToAdd;
    
    return {
      'added_seconds': secondsToAdd,
      'total_remaining': _restTimeRemaining,
      'message': 'Added $secondsToAdd seconds. Total rest time: $_restTimeRemaining seconds.',
    };
  }
  
  /// Replace current exercise with a different one
  Future<Map<String, dynamic>> _replaceExercise(String? newExerciseName, String? muscleGroup) async {
    if (_currentWorkout == null) {
      return {'error': 'No workout loaded'};
    }
    
    try {
      final exercises = _currentWorkout!['exercises'] as List;
      if (_currentExerciseIndex >= exercises.length) {
        return {'error': 'Invalid exercise index'};
      }
      
      final currentExercise = exercises[_currentExerciseIndex];
      
      // If no exercise name provided, just return the current state
      if (newExerciseName == null || newExerciseName.isEmpty) {
        // If only muscle group is provided, search for similar exercises
        if (muscleGroup != null && muscleGroup.isNotEmpty) {
          final query = _supabase.from('exercises')
              .select('*')
              .ilike('primary_muscle', '%$muscleGroup%')
              .limit(5);
          
          final results = await query;
          
          if (results.isEmpty) {
            return {
              'error': 'No exercises found for muscle group: $muscleGroup',
              'message': 'Try a different muscle group like "chest", "back", "legs", etc.',
            };
          }
          
          // Return options for the AI to choose from
          return {
            'current_exercise': currentExercise['name'],
            'muscle_group': muscleGroup,
            'available_exercises': results.map((e) => {
              'name': e['name'],
              'muscle': e['primary_muscle'],
              'equipment': e['equipment'] ?? 'None',
            }).toList(),
            'message': 'Found ${results.length} exercises for $muscleGroup. Please specify which exercise you want.',
          };
        }
        
        return {'error': 'Please specify an exercise name to replace with'};
      }
      
      // Search for the specific exercise
      final searchQuery = _supabase.from('exercises')
          .select('*')
          .ilike('name', '%$newExerciseName%')
          .limit(1);
      
      final results = await searchQuery;
      
      if (results.isEmpty) {
        return {
          'error': 'Exercise not found: $newExerciseName',
          'message': 'Please try a different exercise name',
        };
      }
      
      final newExercise = results[0];
      
      // Important: Only update the exercise name and related info, keep the structure
      exercises[_currentExerciseIndex] = {
        ...currentExercise,
        'exercise_id': newExercise['id'],
        'name': newExercise['name'],
        'primary_muscle': newExercise['primary_muscle'] ?? currentExercise['primary_muscle'],
        'equipment': newExercise['equipment'] ?? currentExercise['equipment'],
        // Keep the original sets, reps, weight, etc.
        'sets': currentExercise['sets'],
        'reps': currentExercise['reps'],
        'weight': currentExercise['weight'],
        'rest': currentExercise['rest'],
        'order': currentExercise['order'],
      };
      
      // Only reset completed sets for this exercise, not the set index
      // This prevents issues with workout progression
      if (_currentWorkout!.containsKey('completed_sets')) {
        final completedSets = _currentWorkout!['completed_sets'] as List;
        // Remove any completed sets for this exercise
        completedSets.removeWhere((set) => set['exercise_index'] == _currentExerciseIndex);
      }
      
      _updateWorkoutState();
      
      // Update AI context with new workout info
      _updateAIContext();
      
      debugPrint('✅ Exercise replaced successfully');
      debugPrint('   Old: ${currentExercise['name']}');
      debugPrint('   New: ${newExercise['name']}');
      debugPrint('   Muscle: ${newExercise['primary_muscle']}');
      debugPrint('   Equipment: ${newExercise['equipment']}');
      
      return {
        'replaced': currentExercise['name'],
        'new_exercise': newExercise['name'],
        'primary_muscle': newExercise['primary_muscle'],
        'equipment': newExercise['equipment'] ?? 'None',
        'sets': currentExercise['sets'],
        'message': 'Successfully replaced ${currentExercise['name']} with ${newExercise['name']}. You still have ${currentExercise['sets']} sets to complete.',
      };
    } catch (e) {
      debugPrint('❌ Error replacing exercise: $e');
      return {'error': 'Failed to replace exercise: ${e.toString()}'};
    }
  }
  
  /// Search for available exercises
  Future<Map<String, dynamic>> _searchExercises(String? muscleGroup, String? equipment) async {
    try {
      var query = _supabase.from('exercises').select('id, name, primary_muscle, equipment');
      
      if (muscleGroup != null && muscleGroup.isNotEmpty) {
        query = query.ilike('primary_muscle', '%$muscleGroup%');
      }
      
      if (equipment != null && equipment.isNotEmpty) {
        query = query.ilike('equipment', '%$equipment%');
      }
      
      final results = await query.limit(10);
      
      if (results.isEmpty) {
        return {
          'exercises': [],
          'message': 'No exercises found matching your criteria',
        };
      }
      
      // Add context about current workout
      String contextMessage = 'Found ${results.length} exercises';
      if (_currentWorkout != null) {
        final exercises = _currentWorkout!['exercises'] as List;
        contextMessage += '. Note: You can use these to replace exercises in your current workout (${exercises.length} exercises total).';
      }
      
      return {
        'exercises': results.map((e) => {
          'name': e['name'],
          'muscle': e['primary_muscle'],
          'equipment': e['equipment'] ?? 'None',
        }).toList(),
        'total': results.length,
        'message': contextMessage,
        'note': 'These exercises can be used with the replace_exercise function to swap out exercises in your current workout.',
      };
    } catch (e) {
      debugPrint('Error searching exercises: $e');
      return {'error': 'Failed to search exercises: ${e.toString()}'};
    }
  }
  
  /// Handle errors
  void _handleError(Map<String, dynamic> event) {
    final error = event['error'];
    final message = error['message'] ?? 'Unknown error';
    debugPrint('Error from server: $message');
    _errorController.add(message);
  }
  
  /// Send an event through the data channel
  void _sendEvent(Map<String, dynamic> event) {
    if (_dataChannel?.state == RTCDataChannelState.RTCDataChannelOpen) {
      final message = jsonEncode(event);
      _dataChannel!.send(RTCDataChannelMessage(message));
      // Only log important events
      if (!['response.create'].contains(event['type'])) {
        debugPrint('📤 Sent: ${event['type']}');
      }
    } else {
      debugPrint('❌ Cannot send event - data channel not open');
    }
  }
  
  /// Send a text message
  void sendTextMessage(String text) {
    final event = {
      'type': 'conversation.item.create',
      'item': {
        'type': 'message',
        'role': 'user',
        'content': [
          {
            'type': 'input_text',
            'text': text,
          }
        ],
      },
    };
    
    _sendEvent(event);
    
    // Trigger response only if not already processing
    if (!_isProcessingResponse) {
      _isProcessingResponse = true;
      _sendEvent({'type': 'response.create'});
    }
  }
  
  /// Disconnect and clean up
  Future<void> disconnect() async {
    debugPrint('Disconnecting WebRTC...');

    // Stop quality monitoring
    _stopQualityMonitoring();

    // Reset quality monitoring state
    _lastQualityWarning = null;
    _poorQualityCount = 0;

    // Close data channel
    await _dataChannel?.close();
    _dataChannel = null;

    // Stop local stream
    _localStream?.getTracks().forEach((track) {
      track.stop();
    });
    await _localStream?.dispose();
    _localStream = null;

    // Close peer connection
    await _peerConnection?.close();
    _peerConnection = null;

    debugPrint('WebRTC disconnected');
  }

  /// Dispose of resources
  void dispose() {
    _restTimer?.cancel();
    _restTimer = null;
    _stopQualityMonitoring();

    // Close all stream controllers first to prevent "Cannot add new events after calling close" errors
    if (!_connectionStateController.isClosed) {
      _connectionStateController.close();
    }
    if (!_transcriptController.isClosed) {
      _transcriptController.close();
    }
    if (!_errorController.isClosed) {
      _errorController.close();
    }
    if (!_workoutStateController.isClosed) {
      _workoutStateController.close();
    }
    if (!_audioQualityController.isClosed) {
      _audioQualityController.close();
    }
    if (!_connectionQualityController.isClosed) {
      _connectionQualityController.close();
    }

    // Then disconnect WebRTC
    disconnect();

    // Reset quality monitoring state
    _lastQualityWarning = null;
    _poorQualityCount = 0;
  }

  /// Restore workout state after reconnection
  Future<void> _restoreWorkoutStateOnReconnect() async {
    if (_currentWorkout != null) {
      debugPrint('🔄 Restoring workout state after reconnection...');
      debugPrint('   Workout: ${_currentWorkout!['name']}');
      debugPrint('   Exercise: $_currentExerciseIndex/${(_currentWorkout!['exercises'] as List).length}');
      debugPrint('   Set: $_currentSetIndex');

      // Update AI context with current state
      _updateAIContext();
      _updateWorkoutState();

      // Send context update to AI after session is ready
      Future.delayed(const Duration(seconds: 2), () async {
        await _sendContextUpdate();
      });

      debugPrint('✅ Workout state restored successfully');
    }
  }

  /// Send context update to AI
  Future<void> _sendContextUpdate() async {
    if (!isConnected || _currentWorkout == null) return;

    final exercises = _currentWorkout!['exercises'] as List;
    final currentExercise = exercises[_currentExerciseIndex];

    final contextMessage = {
      'type': 'conversation.item.create',
      'item': {
        'type': 'message',
        'role': 'user',
        'content': [
          {
            'type': 'text',
            'text': 'Workout state restored. Currently on ${currentExercise['name']}, set ${_currentSetIndex + 1} of ${currentExercise['sets']}. Ready to continue.',
          }
        ]
      }
    };

    _sendEvent(contextMessage);
  }

  /// Start monitoring audio and connection quality
  void _startQualityMonitoring() {
    // Monitor connection quality every 10 seconds (reduced frequency)
    _connectionQualityTimer = Timer.periodic(const Duration(seconds: 10), (timer) {
      if (!isConnected) {
        timer.cancel();
        _connectionQualityTimer = null;
        return;
      }
      _monitorConnectionQuality();
    });

    // Monitor audio quality every 5 seconds (reduced frequency)
    _audioLevelTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (!isConnected) {
        timer.cancel();
        _audioLevelTimer = null;
        return;
      }
      _monitorAudioQuality();
    });
  }

  /// Monitor WebRTC connection quality
  void _monitorConnectionQuality() async {
    if (_peerConnection == null) return;

    try {
      final stats = await _peerConnection!.getStats();

      // Extract relevant metrics from stats
      double latency = 0.0;
      double packetLoss = 0.0;
      int bytesReceived = 0;
      int bytesSent = 0;

      for (final report in stats) {
        if (report.type == 'inbound-rtp') {
          packetLoss = (report.values['packetsLost'] as num?)?.toDouble() ?? 0.0;
          bytesReceived = (report.values['bytesReceived'] as num?)?.toInt() ?? 0;
        } else if (report.type == 'outbound-rtp') {
          bytesSent = (report.values['bytesSent'] as num?)?.toInt() ?? 0;
        } else if (report.type == 'candidate-pair' && report.values['state'] == 'succeeded') {
          latency = (report.values['currentRoundTripTime'] as num?)?.toDouble() ?? 0.0;
          latency *= 1000; // Convert to milliseconds
        }
      }

      final metrics = ConnectionQualityMetrics.fromStats(
        connectionState: _peerConnection!.connectionState!,
        iceConnectionState: _peerConnection!.iceConnectionState!,
        latency: latency,
        packetLoss: packetLoss,
        bytesReceived: bytesReceived,
        bytesSent: bytesSent,
      );

      _connectionQualityController.add(metrics);

      // Check for poor connection and provide feedback (with debouncing)
      if (metrics.qualityDescription == 'Poor' || metrics.latency > 2000) {
        _handlePoorConnectionQuality(metrics);
      } else if (metrics.qualityDescription == 'Fair') {
        // Reset poor quality count for fair connections
        _poorQualityCount = 0;
      }

    } catch (e) {
      debugPrint('Error monitoring connection quality: $e');
    }
  }

  /// Monitor audio quality
  void _monitorAudioQuality() {
    // Simulate audio level monitoring (in a real implementation, you'd get this from the audio stream)
    // For now, we'll use placeholder values and focus on the infrastructure

    // In a real implementation, you would:
    // 1. Analyze the audio stream for volume levels
    // 2. Detect background noise patterns
    // 3. Calculate signal-to-noise ratio

    final audioLevel = 0.5 + (DateTime.now().millisecond % 100) / 200; // Simulated
    final backgroundNoise = 0.1 + (DateTime.now().millisecond % 50) / 500; // Simulated

    _currentAudioLevel = audioLevel;
    _backgroundNoiseLevel = backgroundNoise;

    final metrics = AudioQualityMetrics.fromAudioLevel(audioLevel, backgroundNoise);
    _audioQualityController.add(metrics);

    // Update quality flag
    _isAudioQualityGood = metrics.isGoodQuality;

    // Provide feedback for poor audio quality
    if (!metrics.isGoodQuality) {
      _handlePoorAudioQuality(metrics);
    }
  }

  /// Handle poor connection quality
  void _handlePoorConnectionQuality(ConnectionQualityMetrics metrics) {
    _poorQualityCount++;

    // Only show warnings after threshold is reached and cooldown has passed
    final now = DateTime.now();
    final shouldShowWarning = _poorQualityCount >= _poorQualityThreshold &&
        (_lastQualityWarning == null ||
         now.difference(_lastQualityWarning!).inSeconds >= _qualityWarningCooldown);

    if (shouldShowWarning) {
      debugPrint('⚠️ Poor connection quality detected: ${metrics.qualityDescription}');
      _lastQualityWarning = now;
      _poorQualityCount = 0; // Reset counter after showing warning

      if (metrics.latency > 2000) {
        _errorController.add('High latency detected. Consider moving closer to your router.');
      } else if (!metrics.isStable) {
        _errorController.add('Connection unstable. Checking network...');
      }
    }
  }

  /// Handle poor audio quality
  void _handlePoorAudioQuality(AudioQualityMetrics metrics) {
    // Only show audio quality warnings if they're truly poor (not just fair)
    if (metrics.qualityDescription == 'Poor') {
      final now = DateTime.now();
      final shouldShowWarning = _lastQualityWarning == null ||
          now.difference(_lastQualityWarning!).inSeconds >= _qualityWarningCooldown;

      if (shouldShowWarning) {
        debugPrint('⚠️ Poor audio quality detected: ${metrics.qualityDescription}');
        _lastQualityWarning = now;

        if (metrics.signalToNoiseRatio < 0.2) {
          _errorController.add('Background noise is high. Try moving to a quieter area.');
        } else if (metrics.audioLevel < 0.1) {
          _errorController.add('Audio level is low. Speak closer to your device.');
        }
      }
    }
  }

  /// Create enhanced error with user guidance
  EnhancedError _createEnhancedError(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('permission') || errorString.contains('microphone')) {
      return EnhancedError.microphonePermissionDenied();
    } else if (errorString.contains('network') || errorString.contains('connection')) {
      return EnhancedError.connectionFailed(error.toString());
    } else {
      return EnhancedError(
        code: 'UNKNOWN_ERROR',
        message: error.toString(),
        userFriendlyMessage: 'Something went wrong with the AI coach',
        troubleshootingSteps: [
          'Check your internet connection',
          'Restart the app',
          'Try again in a few moments',
        ],
        fallbackAction: 'Use manual workout tracking',
        severity: ErrorSeverity.medium,
        timestamp: DateTime.now(),
      );
    }
  }

  /// Stop quality monitoring
  void _stopQualityMonitoring() {
    _audioLevelTimer?.cancel();
    _audioLevelTimer = null;
    _connectionQualityTimer?.cancel();
    _connectionQualityTimer = null;
  }
}

/// Event for transcript updates
class TranscriptEvent {
  final String text;
  final bool isUser;
  final bool isDelta;
  final bool isComplete;
  
  TranscriptEvent({
    required this.text,
    required this.isUser,
    this.isDelta = false,
    this.isComplete = false,
  });
}