import 'package:flutter_webrtc/flutter_webrtc.dart';

/// Audio quality metrics for monitoring voice interaction quality
class AudioQualityMetrics {
  final double audioLevel;
  final double backgroundNoiseLevel;
  final double signalToNoiseRatio;
  final bool isGoodQuality;
  final String qualityDescription;
  final DateTime timestamp;

  const AudioQualityMetrics({
    required this.audioLevel,
    required this.backgroundNoiseLevel,
    required this.signalToNoiseRatio,
    required this.isGoodQuality,
    required this.qualityDescription,
    required this.timestamp,
  });

  /// Create metrics from audio level data
  factory AudioQualityMetrics.fromAudioLevel(double audioLevel, double backgroundNoise) {
    final snr = audioLevel > 0 ? (audioLevel - backgroundNoise) / audioLevel : 0.0;
    final isGood = snr > 0.3 && audioLevel > 0.1; // Thresholds for good quality
    
    String description;
    if (snr > 0.5) {
      description = 'Excellent';
    } else if (snr > 0.3) {
      description = 'Good';
    } else if (snr > 0.1) {
      description = 'Fair';
    } else {
      description = 'Poor';
    }

    return AudioQualityMetrics(
      audioLevel: audioLevel,
      backgroundNoiseLevel: backgroundNoise,
      signalToNoiseRatio: snr,
      isGoodQuality: isGood,
      qualityDescription: description,
      timestamp: DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'AudioQualityMetrics(level: ${audioLevel.toStringAsFixed(2)}, '
           'noise: ${backgroundNoiseLevel.toStringAsFixed(2)}, '
           'snr: ${signalToNoiseRatio.toStringAsFixed(2)}, '
           'quality: $qualityDescription)';
  }
}

/// Connection quality metrics for WebRTC monitoring
class ConnectionQualityMetrics {
  final RTCPeerConnectionState connectionState;
  final RTCIceConnectionState iceConnectionState;
  final double latency;
  final double packetLoss;
  final int bytesReceived;
  final int bytesSent;
  final bool isStable;
  final String qualityDescription;
  final DateTime timestamp;

  const ConnectionQualityMetrics({
    required this.connectionState,
    required this.iceConnectionState,
    required this.latency,
    required this.packetLoss,
    required this.bytesReceived,
    required this.bytesSent,
    required this.isStable,
    required this.qualityDescription,
    required this.timestamp,
  });

  /// Create metrics from WebRTC stats
  factory ConnectionQualityMetrics.fromStats({
    required RTCPeerConnectionState connectionState,
    required RTCIceConnectionState iceConnectionState,
    double latency = 0.0,
    double packetLoss = 0.0,
    int bytesReceived = 0,
    int bytesSent = 0,
  }) {
    final isStable = connectionState == RTCPeerConnectionState.RTCPeerConnectionStateConnected &&
                    iceConnectionState == RTCIceConnectionState.RTCIceConnectionStateConnected &&
                    packetLoss < 0.05; // Less than 5% packet loss

    String description;
    if (isStable && latency < 200) {
      description = 'Excellent';
    } else if (isStable && latency < 500) {
      description = 'Good';
    } else if (connectionState == RTCPeerConnectionState.RTCPeerConnectionStateConnected) {
      description = 'Fair';
    } else {
      description = 'Poor';
    }

    return ConnectionQualityMetrics(
      connectionState: connectionState,
      iceConnectionState: iceConnectionState,
      latency: latency,
      packetLoss: packetLoss,
      bytesReceived: bytesReceived,
      bytesSent: bytesSent,
      isStable: isStable,
      qualityDescription: description,
      timestamp: DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'ConnectionQualityMetrics(state: $connectionState, '
           'latency: ${latency.toStringAsFixed(0)}ms, '
           'loss: ${(packetLoss * 100).toStringAsFixed(1)}%, '
           'quality: $qualityDescription)';
  }
}

/// Enhanced error information with actionable guidance
class EnhancedError {
  final String code;
  final String message;
  final String userFriendlyMessage;
  final List<String> troubleshootingSteps;
  final String? fallbackAction;
  final ErrorSeverity severity;
  final DateTime timestamp;

  const EnhancedError({
    required this.code,
    required this.message,
    required this.userFriendlyMessage,
    required this.troubleshootingSteps,
    this.fallbackAction,
    required this.severity,
    required this.timestamp,
  });

  /// Create error from WebRTC connection failure
  factory EnhancedError.connectionFailed(String originalError) {
    return EnhancedError(
      code: 'CONNECTION_FAILED',
      message: originalError,
      userFriendlyMessage: 'Unable to connect to AI coach',
      troubleshootingSteps: [
        'Check your internet connection',
        'Ensure microphone permissions are granted',
        'Try moving to an area with better signal',
        'Restart the app if the problem persists',
      ],
      fallbackAction: 'Use manual workout tracking',
      severity: ErrorSeverity.high,
      timestamp: DateTime.now(),
    );
  }

  /// Create error from microphone permission denial
  factory EnhancedError.microphonePermissionDenied() {
    return EnhancedError(
      code: 'MICROPHONE_PERMISSION_DENIED',
      message: 'Microphone permission denied',
      userFriendlyMessage: 'Microphone access is required for voice control',
      troubleshootingSteps: [
        'Go to Settings > Privacy > Microphone',
        'Enable microphone access for this app',
        'Return to the app and try again',
      ],
      fallbackAction: 'Use touch controls instead',
      severity: ErrorSeverity.high,
      timestamp: DateTime.now(),
    );
  }

  /// Create error from poor audio quality
  factory EnhancedError.poorAudioQuality() {
    return EnhancedError(
      code: 'POOR_AUDIO_QUALITY',
      message: 'Audio quality is poor',
      userFriendlyMessage: 'Having trouble hearing you clearly',
      troubleshootingSteps: [
        'Move to a quieter area if possible',
        'Speak closer to your device',
        'Check if your microphone is blocked',
        'Try using headphones with a microphone',
      ],
      fallbackAction: 'Continue with current settings',
      severity: ErrorSeverity.medium,
      timestamp: DateTime.now(),
    );
  }
}

enum ErrorSeverity {
  low,
  medium,
  high,
  critical,
}
