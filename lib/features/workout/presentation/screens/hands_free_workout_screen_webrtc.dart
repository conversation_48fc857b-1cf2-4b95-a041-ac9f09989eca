import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_webrtc/flutter_webrtc.dart';
import '../../domain/models/workout_session.dart';
import '../../domain/providers/workout_session_provider.dart';
import '../../../../shared/services/openai_realtime_webrtc_service.dart';
import '../../../../core/theme/color_palette.dart';
import '../../domain/models/voice_status.dart';

class HandsFreeWorkoutScreenWebRTC extends ConsumerStatefulWidget {
  final WorkoutSession workout;
  const HandsFreeWorkoutScreenWebRTC({super.key, required this.workout});

  @override
  ConsumerState<HandsFreeWorkoutScreenWebRTC> createState() => _HandsFreeWorkoutScreenWebRTCState();
}

class _HandsFreeWorkoutScreenWebRTCState extends ConsumerState<HandsFreeWorkoutScreenWebRTC>
    with TickerProviderStateMixin {

  // Services
  late final OpenAIRealtimeWebRTCService _webrtcService;

  // Animation controllers
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  // State variables
  bool _isNavigating = false;
  bool _isDisposed = false;
  bool _isConnecting = false;
  String _connectionStatus = 'Disconnected';
  double _currentWeight = 0.0;
  Map<String, dynamic> _workoutState = {};

  // Voice coaching state
  final List<String> _suggestions = [];

  // Rep tracking state
  int _currentReps = 0;
  bool _isResting = false;
  int _restTimeRemaining = 0;
  bool _isPersonalRecord = false;

  // Connection quality management
  DateTime? _lastQualityWarning;
  int _poorQualityCount = 0;
  static const int _qualityWarningCooldown = 30; // seconds
  static const int _poorQualityThreshold = 5; // consecutive poor quality detections
  
  @override
  void initState() {
    super.initState();

    // Initialize services
    _webrtcService = OpenAIRealtimeWebRTCService();

    // Initialize animations
    _fadeController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    ));

    // Start fade in animation
    _fadeController.forward();

    // Initialize coaching state
    _initializeCoachingState();

    // Initialize weight from workout data
    _initializeWeight();

    // Setup listeners
    _setupWebRTCListeners();

    // Start voice workout after frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startVoiceWorkout();
    });
  }
  
  void _initializeCoachingState() {
    // Initialize coaching state
  }

  void _setupWebRTCListeners() {
    // Connection state
    _webrtcService.connectionState.listen((state) {
      if (mounted) {
        setState(() {
          _connectionStatus = state.toString().split('.').last;
        });

        // Update voice status based on connection
        if (state == RTCPeerConnectionState.RTCPeerConnectionStateConnected) {
          ref.read(workoutSessionProvider.notifier).updateVoiceStatus(VoiceStatus.connected);
        } else if (state == RTCPeerConnectionState.RTCPeerConnectionStateFailed ||
                   state == RTCPeerConnectionState.RTCPeerConnectionStateDisconnected) {
          ref.read(workoutSessionProvider.notifier).updateVoiceStatus(VoiceStatus.disconnected);
        }
      }
    });
    
    // Workout state updates
    _webrtcService.workoutState.listen((state) {
      if (mounted) {
        setState(() {
          _workoutState = state;

          // Sync rest timer state from WebRTC service
          if (state['is_resting'] == true) {
            _isResting = true;
            _restTimeRemaining = state['rest_time_remaining'] ?? 0;
          } else {
            _isResting = false;
            _restTimeRemaining = 0;
          }

          // Check if workout is completed
          if (state['has_workout'] == false &&
              state['completed_sets'] != null &&
              (state['completed_sets'] as List).isNotEmpty) {
            _handleWorkoutCompletion();
          }
        });
        
        // Update UI when sets are logged
        if (state['completed_sets'] != null) {
          final completedSets = state['completed_sets'] as List;
          if (completedSets.isNotEmpty) {
            final lastSet = completedSets.last;

            // Handle rep logging for coaching analysis
            _onRepLogged(
              lastSet['reps'] ?? 0,
              (lastSet['weight'] ?? 0.0).toDouble(),
              lastSet['difficulty'] ?? 'medium',
            );

            // Check for PR and update coaching
            if (lastSet['is_pr'] == true) {
              _suggestions.add('Personal Record! You\'re crushing it!');
            }
          }
        }
        
        // Don't automatically sync exercise index - let the WebRTC service manage the workout flow
        // The WebRTC service is the source of truth for workout progression
      }
    });
    
    // Connection quality monitoring
    _webrtcService.connectionQuality.listen((metrics) {
      if (mounted) {
        setState(() {
          _connectionStatus = metrics.qualityDescription;
        });

        // Handle poor connection quality with debouncing
        if (metrics.qualityDescription == 'Poor') {
          _handlePoorConnectionWithDebounce();
        } else {
          // Reset poor quality count for good connections
          _poorQualityCount = 0;
        }
      }
    });

    // Error handling
    _webrtcService.errors.listen((error) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(error),
            backgroundColor: Colors.red,
            action: SnackBarAction(
              label: 'Reconnect',
              onPressed: _handleReconnect,
            ),
          ),
        );
      }
    });


  }

  void _handlePoorConnectionWithDebounce() {
    _poorQualityCount++;

    // Only show warnings after threshold is reached and cooldown has passed
    final now = DateTime.now();
    final shouldShowWarning = _poorQualityCount >= _poorQualityThreshold &&
        (_lastQualityWarning == null ||
         now.difference(_lastQualityWarning!).inSeconds >= _qualityWarningCooldown);

    if (shouldShowWarning && mounted) {
      _lastQualityWarning = now;
      _poorQualityCount = 0; // Reset counter after showing warning

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: const Text('Poor connection detected. Audio may be affected.'),
          backgroundColor: Colors.orange,
          action: SnackBarAction(
            label: 'Reconnect',
            onPressed: _handleReconnect,
          ),
        ),
      );
    }
  }

  Future<void> _handleReconnect() async {
    if (_isConnecting) return;

    // Disconnect first
    await _webrtcService.disconnect();

    // Wait a moment
    await Future.delayed(const Duration(seconds: 1));

    // Reconnect
    _startVoiceWorkout();
  }

  Color _getConnectionColor() {
    if (_isConnecting) return Colors.orange;
    if (_webrtcService.isConnected) {
      // Check for poor connection quality
      if (_connectionStatus.contains('Poor') || _connectionStatus.contains('unstable')) {
        return Colors.orange;
      }
      return Colors.green;
    }
    return Colors.red;
  }

  String _getConnectionDisplayText() {
    if (_isConnecting) return 'Connecting...';
    if (_webrtcService.isConnected) {
      if (_connectionStatus.contains('Poor') || _connectionStatus.contains('unstable')) {
        return 'Poor Quality';
      }
      return 'Connected';
    }
    return 'Tap to Reconnect';
  }

  void _showLogSetDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColorPalette.colorNavySurface,
        title: const Text(
          'Log Set',
          style: TextStyle(color: AppColorPalette.textCreamPrimary),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              style: const TextStyle(color: AppColorPalette.textCreamPrimary),
              decoration: const InputDecoration(
                labelText: 'Reps',
                labelStyle: TextStyle(color: AppColorPalette.textCreamSecondary),
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: AppColorPalette.glassBorder),
                ),
                focusedBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: AppColorPalette.colorNavyBright),
                ),
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                _currentReps = int.tryParse(value) ?? 0;
              },
            ),
            const SizedBox(height: 16),
            TextField(
              style: const TextStyle(color: AppColorPalette.textCreamPrimary),
              decoration: const InputDecoration(
                labelText: 'Weight (lbs)',
                labelStyle: TextStyle(color: AppColorPalette.textCreamSecondary),
                enabledBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: AppColorPalette.glassBorder),
                ),
                focusedBorder: UnderlineInputBorder(
                  borderSide: BorderSide(color: AppColorPalette.colorNavyBright),
                ),
              ),
              keyboardType: TextInputType.number,
              onChanged: (value) {
                _currentWeight = double.tryParse(value) ?? 0.0;
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text(
              'Cancel',
              style: TextStyle(color: AppColorPalette.textCreamSecondary),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _webrtcService.sendTextMessage('Log set: $_currentReps reps at $_currentWeight pounds');
            },
            child: const Text(
              'Log Set',
              style: TextStyle(color: Colors.green),
            ),
          ),
        ],
      ),
    );
  }
  
  void _initializeWeight() {
    final sessionState = ref.read(workoutSessionProvider);
    
    if (sessionState.currentWeight > 0) {
      _currentWeight = sessionState.currentWeight;
      return;
    }
    
    final workout = sessionState.currentWorkout ?? widget.workout;
    final currentExerciseIndex = sessionState.currentExerciseIndex;
    final currentSetIndex = sessionState.currentSetIndex;
    
    if (currentExerciseIndex < workout.exercises.length) {
      final exercise = workout.exercises[currentExerciseIndex];
      if (exercise.targetWeights.isNotEmpty && currentSetIndex < exercise.targetWeights.length) {
        _currentWeight = exercise.targetWeights[currentSetIndex];
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted && _currentWeight > 0) {
            ref.read(workoutSessionProvider.notifier).updateCurrentWeight(_currentWeight);
          }
        });
      }
    }
  }
  
  Future<void> _requestPermissions() async {
    final status = await Permission.microphone.request();
    if (!status.isGranted) {
      throw Exception('Microphone permission not granted');
    }

    // Show audio setup tips
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('💡 Tip: Make sure your volume is up and you\'re in a quiet area'),
          duration: Duration(seconds: 3),
        ),
      );
    }
  }
  
  Future<void> _startVoiceWorkout() async {
    if (_isConnecting || _webrtcService.isConnected) return;

    setState(() {
      _isConnecting = true;
      _connectionStatus = 'Requesting permissions...';
    });

    try {
      // Request microphone permission
      await _requestPermissions();

      setState(() {
        _connectionStatus = 'Connecting to AI coach...';
      });

      // Connect to OpenAI Realtime API with retry logic
      int retryCount = 0;
      const maxRetries = 3;

      while (retryCount < maxRetries) {
        try {
          await _webrtcService.connect();
          break;
        } catch (e) {
          retryCount++;
          if (retryCount >= maxRetries) {
            rethrow;
          }
          setState(() {
            _connectionStatus = 'Retrying connection... ($retryCount/$maxRetries)';
          });
          await Future.delayed(Duration(seconds: retryCount * 2));
        }
      }

      // Wait for stable connection
      setState(() {
        _connectionStatus = 'Stabilizing connection...';
      });
      await Future.delayed(const Duration(seconds: 3));

      // Verify connection is still active
      if (!_webrtcService.isConnected) {
        throw Exception('Connection lost during stabilization');
      }

      setState(() {
        _connectionStatus = 'Loading workout...';
      });

      // Send command to load the specific workout
      _webrtcService.sendTextMessage('Load workout ${widget.workout.id}');

      // Wait for workout to load
      await Future.delayed(const Duration(seconds: 2));

      // Request initial greeting after workout is loaded
      _webrtcService.sendTextMessage('Start workout and tell me what exercise we\'re doing');

      setState(() {
        _isConnecting = false;
        _connectionStatus = 'Connected';
      });

    } catch (e) {
      setState(() {
        _isConnecting = false;
        _connectionStatus = 'Connection Failed';
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to start voice mode: $e'),
            action: SnackBarAction(
              label: 'Retry',
              onPressed: _startVoiceWorkout,
            ),
          ),
        );
      }
    }
  }
  
  Future<void> _stopVoiceWorkout() async {
    if (!_isDisposed) {
      try {
        // Properly disconnect and dispose the service
        await _webrtcService.disconnect();
        _webrtcService.dispose();
      } catch (e) {
        debugPrint('Error stopping voice workout: $e');
      }
    }
  }
  
  @override
  void dispose() {
    _isDisposed = true;

    // Stop voice workout before disposing (fire and forget)
    _stopVoiceWorkout();

    // Dispose animation controllers
    _fadeController.dispose();

    // Note: Don't access ScaffoldMessenger in dispose as the widget tree may be deactivated
    // The snackbars will be automatically cleared when the widget is removed

    super.dispose();
  }

  Future<void> _handleClose() async {
    // Clear any pending snackbars
    if (mounted) {
      ScaffoldMessenger.of(context).clearSnackBars();
    }

    // Disconnect and dispose WebRTC before navigating
    await _webrtcService.disconnect();
    _webrtcService.dispose();

    // Navigate back to dashboard (check mounted after async operation)
    if (mounted) {
      if (context.canPop()) {
        context.pop();
      } else {
        // If can't pop, go to dashboard
        context.go('/dashboard');
      }
    }
  }
  
  void _onRepLogged(int reps, double weight, String difficulty) {
    if (mounted) {
      setState(() {
        _currentReps = reps;
        _currentWeight = weight;

        // Check for personal record
        _isPersonalRecord = weight > _currentWeight;
      });

      // Add simple suggestions based on performance
      if (difficulty == 'easy') {
        _suggestions.add('Consider increasing weight next set');
      } else if (difficulty == 'hard') {
        _suggestions.add('Great effort! Take a good rest');
      }

      if (_isPersonalRecord) {
        _suggestions.add('🎉 Personal Record! Great job!');
      }
    }
  }



  // Note: Rest timer is now managed by the WebRTC service
  // The UI syncs with the service state via the workoutState stream listener

  // Helper methods for the new UI
  Widget _buildTopNavigationBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: AppColorPalette.colorNavySurface.withValues(alpha: 0.5),
        border: Border.all(
          color: AppColorPalette.glassBorder,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Close button
          GestureDetector(
            onTap: _handleClose,
            child: Container(
              padding: const EdgeInsets.all(8),
              child: const Icon(
                Icons.close,
                color: AppColorPalette.textCreamPrimary,
                size: 24,
              ),
            ),
          ),

          // Connection status with reconnect option
          GestureDetector(
            onTap: _webrtcService.isConnected ? null : _handleReconnect,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: _getConnectionColor().withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                  color: _getConnectionColor().withValues(alpha: 0.5),
                  width: 1,
                ),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: _getConnectionColor(),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _getConnectionDisplayText(),
                    style: const TextStyle(
                      color: AppColorPalette.textCreamPrimary,
                      fontSize: 12,
                    ),
                  ),
                  if (!_webrtcService.isConnected && !_isConnecting) ...[
                    const SizedBox(width: 4),
                    const Icon(
                      Icons.refresh,
                      color: AppColorPalette.textCreamPrimary,
                      size: 12,
                    ),
                  ],
                ],
              ),
            ),
          ),

          // Commands button
          GestureDetector(
            onTap: () {
              // Show voice commands help
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Say: "Log set: X reps at Y pounds"'),
                  duration: Duration(seconds: 2),
                ),
              );
            },
            child: Container(
              padding: const EdgeInsets.all(8),
              child: const Icon(
                Icons.mic,
                color: AppColorPalette.textCreamPrimary,
                size: 24,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _onSuggestionDismissed(String suggestion) {
    _suggestions.remove(suggestion);
  }
  
  void _handleWorkoutCompletion() {
    if (!_isNavigating) {
      _isNavigating = true;
      _stopVoiceWorkout();
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (!mounted) return;
        context.pushReplacement(
          '/workout-completion',
          extra: widget.workout,
        );
      });
    }
  }
  
  String _getCurrentExerciseName() {
    // Always use the workout state from WebRTC service as source of truth
    if (_workoutState['has_workout'] != true) {
      return 'Loading workout...';
    }
    
    final exercises = widget.workout.exercises;
    final currentIndex = _workoutState['current_exercise_index'] ?? 0;
    
    // Validate index to prevent out of bounds
    if (currentIndex >= 0 && currentIndex < exercises.length) {
      return exercises[currentIndex].name;
    }
    
    // If we're beyond the last exercise, the workout is complete
    return 'Workout Complete 🎉';
  }
  

  
  int _getCurrentSet() {
    return (_workoutState['current_set_index'] ?? 0) + 1;
  }
  
  int _getTotalSets() {
    final exercises = widget.workout.exercises;
    final currentIndex = _workoutState['current_exercise_index'] ?? 0;

    // Validate index to prevent out of bounds
    if (currentIndex >= 0 && currentIndex < exercises.length) {
      return exercises[currentIndex].sets;
    }

    // If we're beyond exercises, return 0
    return 0;
  }



  @override
  Widget build(BuildContext context) {

    return Scaffold(
      backgroundColor: AppColorPalette.colorBlackPure,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Stack(
          children: [
            // Background gradient
            Container(
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  center: Alignment.topCenter,
                  radius: 1.5,
                  colors: [
                    AppColorPalette.colorNavySurface.withValues(alpha: 0.3),
                    AppColorPalette.colorBlackPure,
                  ],
                ),
              ),
            ),

            // Main content
            SafeArea(
              child: Column(
                children: [
                  // Top navigation bar
                  _buildTopNavigationBar(),

                  // Main content area
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Exercise name
                          Text(
                            _getCurrentExerciseName(),
                            style: const TextStyle(
                              color: AppColorPalette.textCreamPrimary,
                              fontSize: 28,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          const SizedBox(height: 24),

                          // Current rep tracking
                          if (_workoutState['has_workout'] == true) ...[
                            Container(
                              padding: const EdgeInsets.all(24),
                              decoration: BoxDecoration(
                                color: AppColorPalette.colorNavySurface.withValues(alpha: 0.6),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: AppColorPalette.glassBorder,
                                  width: 1,
                                ),
                              ),
                              child: Column(
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                    children: [
                                      // Current reps
                                      Column(
                                        children: [
                                          const Text(
                                            'REPS',
                                            style: TextStyle(
                                              color: AppColorPalette.textCreamSecondary,
                                              fontSize: 12,
                                              fontWeight: FontWeight.w600,
                                              letterSpacing: 1,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            '$_currentReps',
                                            style: const TextStyle(
                                              color: AppColorPalette.textCreamPrimary,
                                              fontSize: 32,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                      // Weight
                                      Column(
                                        children: [
                                          const Text(
                                            'WEIGHT',
                                            style: TextStyle(
                                              color: AppColorPalette.textCreamSecondary,
                                              fontSize: 12,
                                              fontWeight: FontWeight.w600,
                                              letterSpacing: 1,
                                            ),
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            '${_currentWeight.toInt()} lbs',
                                            style: const TextStyle(
                                              color: AppColorPalette.textCreamPrimary,
                                              fontSize: 32,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),

                            const SizedBox(height: 20),
                          ],

                          // Set counter
                          Container(
                            padding: const EdgeInsets.all(24),
                            decoration: BoxDecoration(
                              color: AppColorPalette.colorNavySurface.withValues(alpha: 0.5),
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(
                                color: AppColorPalette.glassBorder,
                                width: 1,
                              ),
                            ),
                            child: Column(
                              children: [
                                const Text(
                                  'SET',
                                  style: TextStyle(
                                    color: AppColorPalette.textCreamSecondary,
                                    fontSize: 14,
                                    fontWeight: FontWeight.w600,
                                    letterSpacing: 2,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  '${_getCurrentSet()}/${_getTotalSets()}',
                                  style: const TextStyle(
                                    color: AppColorPalette.textCreamPrimary,
                                    fontSize: 48,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: 24),

                          // Rest timer
                          if (_isResting) ...[
                            Container(
                              padding: const EdgeInsets.all(20),
                              decoration: BoxDecoration(
                                color: Colors.orange.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Colors.orange.withValues(alpha: 0.5),
                                  width: 1,
                                ),
                              ),
                              child: Column(
                                children: [
                                  const Text(
                                    'REST TIME',
                                    style: TextStyle(
                                      color: Colors.orange,
                                      fontSize: 14,
                                      fontWeight: FontWeight.w600,
                                      letterSpacing: 1,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    '$_restTimeRemaining',
                                    style: const TextStyle(
                                      color: Colors.orange,
                                      fontSize: 36,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const Text(
                                    'seconds',
                                    style: TextStyle(
                                      color: Colors.orange,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 24),
                          ],

                          // Connection status
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            decoration: BoxDecoration(
                              color: _webrtcService.isConnected
                                  ? Colors.green.withValues(alpha: 0.2)
                                  : Colors.red.withValues(alpha: 0.2),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: _webrtcService.isConnected
                                    ? Colors.green.withValues(alpha: 0.5)
                                    : Colors.red.withValues(alpha: 0.5),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Container(
                                  width: 8,
                                  height: 8,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: _webrtcService.isConnected
                                        ? Colors.green
                                        : Colors.red,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  _connectionStatus,
                                  style: const TextStyle(
                                    color: AppColorPalette.textCreamPrimary,
                                    fontSize: 14,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: 32),

                          // Voice instructions and manual controls
                          if (_webrtcService.isConnected) ...[
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: AppColorPalette.colorNavySurface.withValues(alpha: 0.3),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: AppColorPalette.glassBorder,
                                  width: 1,
                                ),
                              ),
                              child: Column(
                                children: [
                                  const Text(
                                    '🎤 Say: "Log set: X reps at Y pounds"',
                                    style: TextStyle(
                                      color: AppColorPalette.textCreamPrimary,
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                  const SizedBox(height: 8),
                                  const Text(
                                    'Other commands: "Skip exercise", "Pause", "What\'s next?"',
                                    style: TextStyle(
                                      color: AppColorPalette.textCreamSecondary,
                                      fontSize: 12,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),

                                  // Audio troubleshooting
                                  if (_connectionStatus.contains('Poor') || _connectionStatus.contains('unstable')) ...[
                                    const SizedBox(height: 12),
                                    Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: Colors.orange.withValues(alpha: 0.2),
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      child: const Column(
                                        children: [
                                          Text(
                                            '⚠️ Audio Issues?',
                                            style: TextStyle(
                                              color: Colors.orange,
                                              fontSize: 12,
                                              fontWeight: FontWeight.w600,
                                            ),
                                          ),
                                          SizedBox(height: 4),
                                          Text(
                                            'Check volume, move closer to WiFi, or use manual controls below',
                                            style: TextStyle(
                                              color: Colors.orange,
                                              fontSize: 10,
                                            ),
                                            textAlign: TextAlign.center,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                            ),

                            const SizedBox(height: 16),

                            // Manual controls fallback
                            Column(
                              children: [
                                // Primary action buttons
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                  children: [
                                    // Log set button
                                    GestureDetector(
                                      onTap: _showLogSetDialog,
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                                        decoration: BoxDecoration(
                                          color: Colors.green.withValues(alpha: 0.2),
                                          borderRadius: BorderRadius.circular(8),
                                          border: Border.all(
                                            color: Colors.green.withValues(alpha: 0.5),
                                            width: 1,
                                          ),
                                        ),
                                        child: const Text(
                                          'Log Set',
                                          style: TextStyle(
                                            color: Colors.green,
                                            fontSize: 14,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                    ),

                                    // Start rest button
                                    GestureDetector(
                                      onTap: () => _webrtcService.sendTextMessage('Start rest timer for 90 seconds'),
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                                        decoration: BoxDecoration(
                                          color: Colors.orange.withValues(alpha: 0.2),
                                          borderRadius: BorderRadius.circular(8),
                                          border: Border.all(
                                            color: Colors.orange.withValues(alpha: 0.5),
                                            width: 1,
                                          ),
                                        ),
                                        child: const Text(
                                          'Start Rest',
                                          style: TextStyle(
                                            color: Colors.orange,
                                            fontSize: 14,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),

                                const SizedBox(height: 12),

                                // Secondary action button
                                GestureDetector(
                                  onTap: () {
                                    _webrtcService.sendTextMessage('Skip exercise');
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 10),
                                    decoration: BoxDecoration(
                                      color: AppColorPalette.colorNavySurface.withValues(alpha: 0.5),
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color: AppColorPalette.glassBorder,
                                        width: 1,
                                      ),
                                    ),
                                    child: const Text(
                                      'Skip Exercise',
                                      style: TextStyle(
                                        color: AppColorPalette.textCreamPrimary,
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],

                          // Suggestions
                          if (_suggestions.isNotEmpty) ...[
                            const SizedBox(height: 16),
                            ...(_suggestions.map((suggestion) => Container(
                              margin: const EdgeInsets.only(bottom: 8),
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.amber.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: Colors.amber.withValues(alpha: 0.5),
                                  width: 1,
                                ),
                              ),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      suggestion,
                                      style: const TextStyle(
                                        color: Colors.amber,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                  GestureDetector(
                                    onTap: () => _onSuggestionDismissed(suggestion),
                                    child: const Icon(
                                      Icons.close,
                                      color: Colors.amber,
                                      size: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ))),
                          ],
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Loading overlay
            if (_isConnecting)
              Container(
                color: AppColorPalette.colorBlackPure.withValues(alpha: 0.8),
                child: Center(
                  child: Container(
                    padding: const EdgeInsets.all(32),
                    decoration: BoxDecoration(
                      color: AppColorPalette.colorNavySurface,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: AppColorPalette.glassBorder,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppColorPalette.colorNavyBright,
                          ),
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'Connecting to AI Coach',
                          style: TextStyle(
                            color: AppColorPalette.textCreamPrimary,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          _connectionStatus,
                          style: const TextStyle(
                            color: AppColorPalette.textCreamSecondary,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
